import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  Alert,
  IconButton,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Divider,
  Card,
  CardContent,
  CardHeader,

} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Close as CloseIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceItem, InvoiceCustomer } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';
import { authService } from '../../../services/auth.service';
import { salesTaxService, SalesTaxOption } from '../../../services/sales-tax.service';
import JournalLineTable, { JournalLineItem } from '../../../shared/components/JournalLineTable';

const CreateInvoicePage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customers, setCustomers] = useState<InvoiceCustomer[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [outputTaxes, setOutputTaxes] = useState<SalesTaxOption[]>([]);
  
  // Form data
  const [formData, setFormData] = useState<Partial<Invoice>>({
    customer: 0,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    status: 'draft',
    items: [],
    subtotal: 0,
    tax_total: 0,
    total_amount: 0,
    notes: '',
    terms: 'Payment due within 30 days',
  });

  // Invoice line items for the table
  const [invoiceLines, setInvoiceLines] = useState<JournalLineItem[]>([
    {
      id: '1',
      account_id: null,
      description: '',
      amount: 0,
      sales_tax: null,
      sales_tax_rate: 0,
      sales_tax_amount: 0,
      product_id: null,
      product_name: '',
      quantity: 1,
      unit_price: 0,
    }
  ]);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Set test token for API access (temporary for testing)
        if (!authService.isAuthenticated()) {
          authService.setTestToken('e15bc01f831c5111f413f534ef82288744cb7d41');
        }

        // Load customers, products, and output taxes from API
        const [customersData, productsData, outputTaxesData] = await Promise.all([
          invoiceService.getCustomers(),
          invoiceService.getProducts(),
          salesTaxService.getOutputTaxes()
        ]);

        setCustomers(customersData);
        setProducts(productsData);
        setOutputTaxes(outputTaxesData);

        // If editing, load the invoice
        if (isEditing && id) {
          const invoice = await invoiceService.getInvoice(parseInt(id));
          setFormData(invoice);
        }

      } catch (err) {
        console.error('Error loading data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');

        // Fallback to mock data if API fails
        setCustomers([
          { id: 1, display_name: 'Acme Corp', email: '<EMAIL>', payment_terms: 'net_30' },
          { id: 2, display_name: 'Tech Solutions Ltd', email: '<EMAIL>', payment_terms: 'net_15' },
          { id: 3, display_name: 'Global Industries', email: '<EMAIL>', payment_terms: 'net_45' },
        ]);

        setProducts([
          { id: 1, name: 'Consulting Services', sales_price: 150, description: 'Professional consulting' },
          { id: 2, name: 'Software License', sales_price: 500, description: 'Annual software license' },
          { id: 3, name: 'Training Program', sales_price: 300, description: 'Staff training program' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  // Handlers
  const handleFieldChange = (field: keyof Invoice, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate due date when customer changes
    if (field === 'customer' && value) {
      const customer = customers.find(c => c.id === value);
      if (customer?.payment_terms && formData.invoice_date) {
        const dueDate = invoiceService.calculateDueDate(formData.invoice_date, customer.payment_terms);
        setFormData(prev => ({ ...prev, due_date: dueDate }));
      }
    }
  };



  // Line item management for JournalLineTable
  const handleLineChange = (lineId: string, field: string, value: any) => {
    setInvoiceLines(prev => prev.map(line => {
      if (line.id === lineId) {
        const updatedLine = { ...line, [field]: value };

        // Auto-calculate line total when quantity or unit_price changes
        if (field === 'quantity' || field === 'unit_price') {
          const quantity = field === 'quantity' ? value : updatedLine.quantity || 1;
          const unitPrice = field === 'unit_price' ? value : updatedLine.unit_price || 0;
          updatedLine.amount = quantity * unitPrice;
        }

        // If product is selected, update related fields
        if (field === 'product_id' && value) {
          const product = products.find(p => p.id === value);
          if (product) {
            updatedLine.product_name = product.name;
            updatedLine.description = product.description || product.name;
            updatedLine.unit_price = product.sales_price || 0;
            updatedLine.amount = (updatedLine.quantity || 1) * (product.sales_price || 0);
          }
        }

        // If product is cleared, clear related fields
        if (field === 'product_id' && !value) {
          updatedLine.product_name = '';
          updatedLine.description = '';
          updatedLine.unit_price = 0;
          updatedLine.amount = 0;
        }

        return updatedLine;
      }
      return line;
    }));
  };

  const handleAddLine = () => {
    const newLine: JournalLineItem = {
      id: Date.now().toString(),
      account_id: null,
      description: '',
      amount: 0,
      sales_tax: null,
      sales_tax_rate: 0,
      sales_tax_amount: 0,
      product_id: null,
      product_name: '',
      quantity: 1,
      unit_price: 0,
    };
    setInvoiceLines(prev => [...prev, newLine]);
  };

  const handleRemoveLine = (lineId: string) => {
    setInvoiceLines(prev => prev.filter(line => line.id !== lineId));
  };

  // Calculate totals from invoice lines
  const calculateInvoiceTotals = () => {
    const subtotal = invoiceLines.reduce((sum, line) => sum + (line.amount || 0), 0);
    const taxTotal = invoiceLines.reduce((sum, line) => sum + (line.sales_tax_amount || 0), 0);
    const totalAmount = subtotal + taxTotal;

    return { subtotal, taxTotal, totalAmount };
  };

  // Update form data totals when lines change
  useEffect(() => {
    const { subtotal, taxTotal, totalAmount } = calculateInvoiceTotals();
    setFormData(prev => ({
      ...prev,
      subtotal,
      tax_total: taxTotal,
      total_amount: totalAmount
    }));
  }, [invoiceLines]);

  const handleSave = async (saveAndClose = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.customer) {
        throw new Error('Please select a customer');
      }
      if (!formData.items?.length) {
        throw new Error('Please add at least one line item');
      }

      const invoiceData = {
        customer: formData.customer!,
        invoice_date: formData.invoice_date!,
        due_date: formData.due_date!,
        status: formData.status!,
        items: formData.items!,
        subtotal: formData.subtotal || 0,
        tax_total: formData.tax_total || 0,
        total_amount: formData.total_amount || 0,
        notes: formData.notes || '',
        terms: formData.terms || '',
      };

      if (isEditing && id) {
        await invoiceService.updateInvoice(parseInt(id), invoiceData);
      } else {
        await invoiceService.createInvoice(invoiceData);
      }

      if (saveAndClose) {
        navigate('/dashboard/sales/invoices');
      } else {
        // Stay on page but show success message
        setError(null);
        // Could show a success snackbar here
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/sales/invoices');
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer);

  return (
    <Box sx={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1300,
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: 'white',
    }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: 2,
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Typography variant="h5">
          {isEditing ? 'Edit Invoice' : 'Create New Invoice'}
        </Typography>
        <IconButton onClick={handleCancel}>
          <CloseIcon />
        </IconButton>
      </Box>
      
      {/* Main Content */}
      <Box sx={{ 
        flexGrow: 1, 
        overflow: 'auto', 
        p: 3,
        backgroundColor: '#f5f5f5'
      }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Invoice Details */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Invoice Details" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={customers}
                  getOptionLabel={(option) => option.display_name}
                  value={selectedCustomer || null}
                  onChange={(_, value) => handleFieldChange('customer', value?.id || 0)}
                  renderInput={(params) => (
                    <TextField {...params} label="Customer" required fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Invoice Date"
                  value={dayjs(formData.invoice_date)}
                  onChange={(date) => handleFieldChange('invoice_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Due Date"
                  value={dayjs(formData.due_date)}
                  onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleFieldChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="sent">Sent</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="overdue">Overdue</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Invoice Line Items" />
          <CardContent>
            <JournalLineTable
              lines={invoiceLines}
              accounts={[]} // Not needed for invoice
              salesTaxes={outputTaxes}
              onLineChange={handleLineChange}
              onAddLine={handleAddLine}
              onRemoveLine={handleRemoveLine}
              currencySymbol="$"
              tableMode="custom"
              showAccountColumn={false}
              showDescriptionColumn={false} // We'll handle description in additional columns
              showAmountColumn={true}
              showSalesTaxColumn={true}
              showActionsColumn={true}
              amountColumnLabel="Line Total"
              salesTaxColumnLabel="Output Tax"
              additionalColumns={[
                {
                  key: 'product_id',
                  label: 'Product',
                  width: 200,
                  render: (line, onChange) => (
                    <Autocomplete
                      fullWidth
                      size="medium"
                      options={products}
                      getOptionLabel={(option) => option.name}
                      value={products.find(p => p.id === line.product_id) || null}
                      onChange={(_, value) => onChange(value?.id || null)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select product"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              minHeight: '48px',
                            },
                            '& .MuiOutlinedInput-input': {
                              padding: '12px 14px',
                            }
                          }}
                        />
                      )}
                    />
                  )
                },
                {
                  key: 'description',
                  label: 'Description',
                  width: 250,
                  render: (line, onChange) => (
                    <Typography
                      variant="body2"
                      sx={{
                        padding: '12px 14px',
                        minHeight: '48px',
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        color: line.description ? 'text.primary' : 'text.secondary',
                        fontStyle: line.description ? 'normal' : 'italic'
                      }}
                    >
                      {line.description || 'Select a product to see description'}
                    </Typography>
                  )
                },
                {
                  key: 'quantity',
                  label: 'Qty',
                  width: 100,
                  render: (line, onChange) => (
                    <TextField
                      fullWidth
                      size="medium"
                      type="number"
                      value={line.quantity || 1}
                      onChange={(e) => onChange(Number(e.target.value) || 1)}
                      inputProps={{ min: 0, step: 0.01 }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '48px',
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '12px 14px',
                        }
                      }}
                    />
                  )
                },
                {
                  key: 'unit_price',
                  label: 'Unit Price',
                  width: 120,
                  render: (line, onChange) => (
                    <FormattedCurrencyInput
                      fullWidth
                      size="medium"
                      name={`unit_price_${line.id}`}
                      value={line.unit_price || 0}
                      onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
                      currencySymbol="$"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '48px',
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '12px 14px',
                        }
                      }}
                    />
                  )
                }
              ]}
            />

            {/* Totals */}
            {invoiceLines.length > 0 && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ minWidth: 300 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Subtotal:</Typography>
                    <Typography>{formatCurrency(formData.subtotal || 0)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Tax:</Typography>
                    <Typography>{formatCurrency(formData.tax_total || 0)}</Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6">{formatCurrency(formData.total_amount || 0)}</Typography>
                  </Box>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Notes and Terms */}
        <Card>
          <CardHeader title="Additional Information" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => handleFieldChange('notes', e.target.value)}
                  placeholder="Internal notes (not shown to customer)"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Terms & Conditions"
                  multiline
                  rows={4}
                  value={formData.terms}
                  onChange={(e) => handleFieldChange('terms', e.target.value)}
                  placeholder="Payment terms and conditions"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
      
      {/* Footer */}
      <Box sx={{ 
        p: 2, 
        borderTop: 1, 
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Button
          variant="outlined"
          onClick={handleCancel}
          startIcon={<ArrowBackIcon />}
        >
          Cancel
        </Button>
        <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
            onClick={() => handleSave(false)}
            disabled={loading}
            startIcon={<SaveIcon />}
          >
            Save Draft
        </Button>
        <Button
          variant="contained"
            onClick={() => handleSave(true)}
            disabled={loading}
          startIcon={<SaveIcon />}
        >
            {loading ? 'Saving...' : 'Save & Close'}
        </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default CreateInvoicePage; 