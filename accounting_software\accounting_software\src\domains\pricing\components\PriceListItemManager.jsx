// src/components/pricing/PriceListItemManager.jsx
import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { Table, Button, Modal, Form, Input, InputNumber, DatePicker, Select, message, Card, Tag, Space } from 'antd';
import { pricingService } from '../../services/pricingService';
import moment from 'moment';

const { Option } = Select;

const PriceListItemManager = () => {
  const { priceListId } = useParams();
  const history = useHistory();
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [priceList, setPriceList] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState(null);

  useEffect(() => {
    if (priceListId) {
      fetchPriceList();
      fetchItems();
      fetchProducts();
    }
  }, [priceListId]);

  const fetchPriceList = async () => {
    try {
      const response = await pricingService.getPriceList(priceListId);
      setPriceList(response.data);
    } catch (error) {
      message.error('Failed to fetch price list details');
    }
  };

  const fetchItems = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getPriceListItems({ price_list: priceListId });
      setItems(response.data);
    } catch (error) {
      message.error('Failed to fetch price list items');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await axios.get('/api/products/');
      setProducts(response.data);
    } catch (error) {
      message.error('Failed to fetch products');
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    form.setFieldsValue({
      ...record,
      product: record.product.id,
      effective_date: moment(record.effective_date),
      expiry_date: record.expiry_date ? moment(record.expiry_date) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      values.price_list = priceListId;
      values.effective_date = values.effective_date.format('YYYY-MM-DD');
      values.expiry_date = values.expiry_date?.format('YYYY-MM-DD');
      
      if (editMode) {
        await pricingService.updatePriceListItem(currentId, values);
        message.success('Price list item updated successfully');
      } else {
        await pricingService.createPriceListItem(values);
        message.success('Price list item added successfully');
      }
      
      setModalVisible(false);
      fetchItems();
    } catch (error) {
      message.error('Failed to save price list item');
    }
  };

  const handleDelete = async (id) => {
    try {
      await pricingService.deletePriceListItem(id);
      message.success('Price list item deleted successfully');
      fetchItems();
    } catch (error) {
      message.error('Failed to delete price list item');
    }
  };

  const columns = [
    {
      title: 'Product Code',
      dataIndex: 'product_code',
      key: 'product_code',
    },
    {
      title: 'Product Name',
      dataIndex: 'product_name',
      key: 'product_name',
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
    },
    {
      title: 'Unit Price',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (value) => `$${value.toFixed(2)}`,
      sorter: (a, b) => a.unit_price - b.unit_price,
    },
    {
      title: 'Min Qty',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
      sorter: (a, b) => a.min_quantity - b.min_quantity,
    },
    {
      title: 'Discount %',
      dataIndex: 'discount_percent',
      key: 'discount_percent',
      render: (value) => `${value}%`,
      sorter: (a, b) => a.discount_percent - b.discount_percent,
    },
    {
      title: 'Effective Date',
      dataIndex: 'effective_date',
      key: 'effective_date',
      render: (date) => moment(date).format('YYYY-MM-DD'),
      sorter: (a, b) => new Date(a.effective_date) - new Date(b.effective_date),
    },
    {
      title: 'Expiry Date',
      dataIndex: 'expiry_date',
      key: 'expiry_date',
      render: (date) => date ? moment(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>Edit</Button>
          <Button type="link" size="small" danger onClick={() => handleDelete(record.id)}>Delete</Button>
        </Space>
      ),
    },
  ];

  if (!priceList) {
    return <div>Loading...</div>;
  }

  return (
    <Card 
      title={`Price List: ${priceList.name}`} 
      extra={<Button type="primary" onClick={handleCreate}>Add Product Price</Button>}
    >
      <div style={{ marginBottom: 16 }}>
        <p>Currency: {priceList.currency}</p>
        <p>
          Valid: {moment(priceList.valid_from).format('YYYY-MM-DD')} to{' '}
          {priceList.valid_to ? moment(priceList.valid_to).format('YYYY-MM-DD') : 'No expiry'}
        </p>
      </div>
      
      <Table 
        columns={columns}
        dataSource={items}
        rowKey="id"
        loading={loading}
        bordered
      />
      
      <Modal
        title={editMode ? 'Edit Product Price' : 'Add Product to Price List'}
        visible={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        okText={editMode ? 'Update' : 'Add'}
        cancelText="Cancel"
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="product"
            label="Product"
            rules={[{ required: true, message: 'Please select a product' }]}
          >
            <Select showSearch optionFilterProp="children" placeholder="Select product">
              {products.map(product => (
                <Option key={product.id} value={product.id}>
                  {product.code} - {product.name} ({product.uom})
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="unit_price"
            label="Unit Price"
            rules={[{ required: true, message: 'Please enter unit price' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              min={0}
              step={0.01}
              precision={2}
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>
          
          <Form.Item
            name="min_quantity"
            label="Minimum Quantity"
            initialValue={1}
            rules={[{ required: true, message: 'Please enter minimum quantity' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              min={0.01}
              step={0.01}
              precision={2}
            />
          </Form.Item>
          
          <Form.Item
            name="discount_percent"
            label="Discount %"
            initialValue={0}
            rules={[{ required: true, message: 'Please enter discount percentage' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              min={0}
              max={100}
              step={0.1}
              precision={2}
              formatter={value => `${value}%`}
              parser={value => value.replace('%', '')}
            />
          </Form.Item>
          
          <Form.Item
            name="effective_date"
            label="Effective Date"
            initialValue={moment()}
            rules={[{ required: true, message: 'Please select effective date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="expiry_date"
            label="Expiry Date (optional)"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default PriceListItemManager;