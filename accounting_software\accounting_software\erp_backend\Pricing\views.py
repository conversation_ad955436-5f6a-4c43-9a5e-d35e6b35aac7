from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from .models import PriceList, PriceListItem, DiscountRule
from .serializers import PriceListSerializer, PriceListItemSerializer, DiscountRuleSerializer
from .services import PricingService

class PriceListViewSet(viewsets.ModelViewSet):
    queryset = PriceList.objects.all()
    serializer_class = PriceListSerializer
    
    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get all items for a specific price list"""
        price_list = self.get_object()
        items = PriceListItem.objects.filter(price_list=price_list)
        serializer = PriceListItemSerializer(items, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default price list"""
        price_list = PriceList.objects.filter(is_default=True, is_active=True).first()
        if not price_list:
            return Response({'detail': 'No default price list found'}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = self.get_serializer(price_list)
        return Response(serializer.data)

class PriceListItemViewSet(viewsets.ModelViewSet):
    queryset = PriceListItem.objects.all()
    serializer_class = PriceListItemSerializer
    
    def get_queryset(self):
        """Optionally filter by price_list and/or product"""
        queryset = super().get_queryset()
        price_list = self.request.query_params.get('price_list')
        product = self.request.query_params.get('product')
        
        if price_list:
            queryset = queryset.filter(price_list=price_list)
        if product:
            queryset = queryset.filter(product=product)
        
        return queryset

class DiscountRuleViewSet(viewsets.ModelViewSet):
    queryset = DiscountRule.objects.all()
    serializer_class = DiscountRuleSerializer
    
    def get_queryset(self):
        """Optionally filter by active status"""
        queryset = super().get_queryset()
        is_active = self.request.query_params.get('is_active')
        
        if is_active:
            is_active = is_active.lower() in ('true', '1', 't')
            queryset = queryset.filter(is_active=is_active)
        
        return queryset

class PricingViewSet(viewsets.ViewSet):
    """Special pricing endpoints"""
    
    @action(detail=False, methods=['get'])
    def get_price(self, request):
        """Get price for a product considering all rules"""
        product_id = request.query_params.get('product')
        customer_id = request.query_params.get('customer')
        quantity = request.query_params.get('quantity', 1)
        date = request.query_params.get('date')
        
        if not product_id:
            return Response({'error': 'product parameter is required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            product = Product.objects.get(pk=product_id)
        except Product.DoesNotExist:
            return Response({'error': 'Product not found'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        customer = None
        if customer_id:
            try:
                customer = Customer.objects.get(pk=customer_id)
            except Customer.DoesNotExist:
                pass  # Proceed without customer-specific pricing
        
        try:
            quantity = float(quantity)
            if quantity <= 0:
                raise ValueError
        except ValueError:
            return Response({'error': 'quantity must be a positive number'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        price = PricingService.get_product_price(
            product=product,
            customer=customer,
            quantity=quantity,
            date=date
        )
        
        if price is None:
            return Response({'error': 'No price found for this product'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            'product_id': product.id,
            'product_code': product.code,
            'product_name': product.name,
            'customer_id': customer.id if customer else None,
            'quantity': quantity,
            'unit_price': price,
            'currency': 'USD'  # In real system, get from price list
        })