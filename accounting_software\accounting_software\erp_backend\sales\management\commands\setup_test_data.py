from django.core.management.base import BaseCommand
from contacts.models import Contact, Customer
from sales.models import Product, ProductCategory
from decimal import Decimal

class Command(BaseCommand):
    help = 'Create test customers and products for invoice testing'

    def handle(self, *args, **options):
        # Create test customers
        customers_created = 0
        test_customers = [
            {
                'name': 'ABC Company Ltd',
                'email': '<EMAIL>',
                'phone': '******-0101',
                'address': '123 Business St, City, State 12345',
                'contact_type': 'customer'
            },
            {
                'name': 'XYZ Corporation',
                'email': '<EMAIL>',
                'phone': '******-0102',
                'address': '456 Corporate Ave, City, State 12346',
                'contact_type': 'customer'
            },
            {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '******-0103',
                'address': '789 Residential Rd, City, State 12347',
                'contact_type': 'customer'
            }
        ]
        
        for customer_data in test_customers:
            contact, created = Contact.objects.get_or_create(
                email=customer_data['email'],
                defaults=customer_data
            )
            if created:
                customers_created += 1
                self.stdout.write(f'Created customer: {contact.name}')
            else:
                self.stdout.write(f'Customer already exists: {contact.name}')
        
        # Create test product category
        category, created = ProductCategory.objects.get_or_create(
            name='General Products',
            defaults={
                'code': 'GEN',
                'description': 'General products and services',
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'Created category: {category.name}')
        
        # Create test products
        products_created = 0
        test_products = [
            {
                'name': 'Consulting Services',
                'sku': 'CONSULT-001',
                'description': 'Professional consulting services',
                'product_type': 'service',
                'unit_price': Decimal('150.00'),
                'cost_price': Decimal('75.00'),
                'status': 'active',
                'taxable': True,
                'track_inventory': False
            },
            {
                'name': 'Software License',
                'sku': 'SW-LIC-001',
                'description': 'Annual software license',
                'product_type': 'product',
                'unit_price': Decimal('500.00'),
                'cost_price': Decimal('200.00'),
                'status': 'active',
                'taxable': True,
                'track_inventory': False
            },
            {
                'name': 'Training Workshop',
                'sku': 'TRAIN-001',
                'description': 'Professional training workshop',
                'product_type': 'service',
                'unit_price': Decimal('300.00'),
                'cost_price': Decimal('100.00'),
                'status': 'active',
                'taxable': True,
                'track_inventory': False
            }
        ]
        
        for product_data in test_products:
            product_data['category'] = category
            product, created = Product.objects.get_or_create(
                sku=product_data['sku'],
                defaults=product_data
            )
            if created:
                products_created += 1
                self.stdout.write(f'Created product: {product.name}')
            else:
                self.stdout.write(f'Product already exists: {product.name}')
        
        self.stdout.write(self.style.SUCCESS(f'''
Test data setup complete:
- {customers_created} new customers created
- {products_created} new products created
- 1 product category created

You can now test invoice creation with this sample data.
        ''')) 