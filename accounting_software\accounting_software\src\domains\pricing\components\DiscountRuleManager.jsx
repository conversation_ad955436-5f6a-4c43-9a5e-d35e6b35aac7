// src/components/pricing/DiscountRuleManager.jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, InputNumber, DatePicker, Select, message, Card, Tag, Space } from 'antd';
import { pricingService } from '../../services/pricingService';
import moment from 'moment';

const { Option } = Select;
const { TextArea } = Input;

const DiscountRuleManager = () => {
  const [discountRules, setDiscountRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [customerGroups, setCustomerGroups] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState(null);

  useEffect(() => {
    fetchDiscountRules();
    fetchProducts();
    fetchCategories();
    fetchCustomerGroups();
  }, []);

  const fetchDiscountRules = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getDiscountRules();
      setDiscountRules(response.data);
    } catch (error) {
      message.error('Failed to fetch discount rules');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await axios.get('/api/products/');
      setProducts(response.data);
    } catch (error) {
      message.error('Failed to fetch products');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/api/product-categories/');
      setCategories(response.data);
    } catch (error) {
      message.error('Failed to fetch product categories');
    }
  };

  const fetchCustomerGroups = async () => {
    try {
      const response = await axios.get('/api/customer-groups/');
      setCustomerGroups(response.data);
    } catch (error) {
      message.error('Failed to fetch customer groups');
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    form.setFieldsValue({
      ...record,
      product_category: record.product_category?.id,
      start_date: moment(record.start_date),
      end_date: record.end_date ? moment(record.end_date) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      values.start_date = values.start_date.format('YYYY-MM-DD');
      values.end_date = values.end_date?.format('YYYY-MM-DD');
      
      if (editMode) {
        await pricingService.updateDiscountRule(currentId, values);
        message.success('Discount rule updated successfully');
      } else {
        await pricingService.createDiscountRule(values);
        message.success('Discount rule created successfully');
      }
      
      setModalVisible(false);
      fetchDiscountRules();
    } catch (error) {
      message.error('Failed to save discount rule');
    }
  };

  const handleDelete = async (id) => {
    try {
      await pricingService.deleteDiscountRule(id);
      message.success('Discount rule deleted successfully');
      fetchDiscountRules();
    } catch (error) {
      message.error('Failed to delete discount rule');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Discount %',
      dataIndex: 'discount_percent',
      key: 'discount_percent',
      render: (value) => `${value}%`,
      sorter: (a, b) => a.discount_percent - b.discount_percent,
    },
    {
      title: 'Min Qty',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
      render: (value) => value || '-',
      sorter: (a, b) => (a.min_quantity || 0) - (b.min_quantity || 0),
    },
    {
      title: 'Customer Group',
      dataIndex: 'customer_group',
      key: 'customer_group',
      render: (value) => value || '-',
    },
    {
      title: 'Product Category',
      dataIndex: 'product_category_name',
      key: 'product_category',
      render: (_, record) => record.product_category?.name || '-',
    },
    {
      title: 'Valid From',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date) => moment(date).format('YYYY-MM-DD'),
      sorter: (a, b) => new Date(a.start_date) - new Date(b.start_date),
    },
    {
      title: 'Valid To',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date) => date ? moment(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value, record) => record.is_active === value,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>Edit</Button>
          <Button type="link" size="small" danger onClick={() => handleDelete(record.id)}>Delete</Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="Discount Rules" extra={<Button type="primary" onClick={handleCreate}>Create Discount Rule</Button>}>
      <Table 
        columns={columns}
        dataSource={discountRules}
        rowKey="id"
        loading={loading}
        bordered
      />
      
      <Modal
        title={editMode ? 'Edit Discount Rule' : 'Create New Discount Rule'}
        visible={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        okText={editMode ? 'Update' : 'Create'}
        cancelText="Cancel"
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={3} />
          </Form.Item>
          
          <Form.Item
            name="discount_percent"
            label="Discount %"
            rules={[{ required: true, message: 'Please enter discount percentage' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              min={0}
              max={100}
              step={0.1}
              precision={2}
              formatter={value => `${value}%`}
              parser={value => value.replace('%', '')}
            />
          </Form.Item>
          
          <Form.Item
            name="min_quantity"
            label="Minimum Quantity (optional)"
          >
            <InputNumber 
              style={{ width: '100%' }}
              min={0.01}
              step={0.01}
              precision={2}
            />
          </Form.Item>
          
          <Form.Item
            name="customer_group"
            label="Customer Group (optional)"
          >
            <Select allowClear>
              {customerGroups.map(group => (
                <Option key={group.id} value={group.name}>
                  {group.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="product_category"
            label="Product Category (optional)"
          >
            <Select allowClear>
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="start_date"
            label="Start Date"
            rules={[{ required: true, message: 'Please select start date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="end_date"
            label="End Date (optional)"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="Active"
            valuePropName="checked"
            initialValue={true}
          >
            <Input type="checkbox" />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default DiscountRuleManager;