import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Autocomplete,
  Divider,
  Alert,
  CircularProgress,
  Paper,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useCustomers } from '../../../contexts/CustomerContext';
import { useProducts } from '../../../contexts/ProductContext';
import { useCompany } from '../../../contexts/CompanyContext';
import { InvoiceLineTable, InvoiceLineItem } from '../../../shared/components';
import { FormattedCurrencyInput } from '../../../shared/components';
import salesTaxService, { SalesTaxOption } from '../../../services/sales-tax.service';
import { v4 as uuidv4 } from 'uuid';

interface InvoiceFormData {
  customer: number | null;
  invoice_date: Dayjs;
  due_date: Dayjs;
  invoice_number: string;
  po_number: string;
  memo: string;
  message_to_customer: string;
  line_items: InvoiceLineItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'sent';
}

const CreateInvoiceFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { customers, loading: customersLoading } = useCustomers();
  const { products, loading: productsLoading } = useProducts();
  const { companyInfo } = useCompany();

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outputTaxes, setOutputTaxes] = useState<SalesTaxOption[]>([]);
  const [defaultTaxRate, setDefaultTaxRate] = useState(0);

  const [formData, setFormData] = useState<InvoiceFormData>({
    customer: null,
    invoice_date: dayjs(),
    due_date: dayjs().add(30, 'day'),
    invoice_number: '',
    po_number: '',
    memo: '',
    message_to_customer: '',
    line_items: [
      {
        id: uuidv4(),
        description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      }
    ],
    subtotal: 0,
    tax_amount: 0,
    total_amount: 0,
    status: 'draft',
  });

  // Load output taxes on component mount
  useEffect(() => {
    const loadOutputTaxes = async () => {
      try {
        const taxes = await salesTaxService.getOutputTaxes();
        setOutputTaxes(taxes);
        
        // Set default tax rate from company settings or first tax rate
        if (companyInfo?.salesTaxRegistered && taxes.length > 0) {
          const defaultRate = taxes.find(tax => tax.description.toLowerCase().includes('standard'))?.rate || taxes[0].rate;
          setDefaultTaxRate(defaultRate);
        }
      } catch (error) {
        console.error('Error loading output taxes:', error);
        setError('Failed to load tax rates');
      }
    };

    loadOutputTaxes();
  }, [companyInfo]);

  // Auto-generate invoice number
  useEffect(() => {
    if (!formData.invoice_number) {
      const invoiceNumber = `INV-${Date.now().toString().slice(-6)}`;
      setFormData(prev => ({ ...prev, invoice_number: invoiceNumber }));
    }
  }, []);

  // Calculate totals when line items change
  useEffect(() => {
    const subtotal = formData.line_items.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = formData.line_items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
    }));
  }, [formData.line_items]);

  // Handlers
  const handleCustomerChange = (customer: any) => {
    setFormData(prev => ({ ...prev, customer: customer?.id || null }));
    
    // Auto-calculate due date based on customer payment terms
    if (customer?.payment_terms && formData.invoice_date) {
      const paymentTermsDays = parseInt(customer.payment_terms.replace(/\D/g, '')) || 30;
      const dueDate = formData.invoice_date.add(paymentTermsDays, 'day');
      setFormData(prev => ({ ...prev, due_date: dueDate }));
    }
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.map(item =>
        item.id === lineId ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleAddLine = () => {
    const newLine: InvoiceLineItem = {
      id: uuidv4(),
      description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      tax_rate: defaultTaxRate,
      tax_amount: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, newLine]
    }));
  };

  const handleRemoveLine = (lineId: string) => {
    if (formData.line_items.length > 1) {
      setFormData(prev => ({
        ...prev,
        line_items: prev.line_items.filter(item => item.id !== lineId)
      }));
    }
  };

  const handleSave = async (status: 'draft' | 'sent') => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.customer) {
        throw new Error('Please select a customer');
      }

      if (formData.line_items.length === 0 || formData.line_items.every(item => !item.description)) {
        throw new Error('Please add at least one line item');
      }

      const invoiceData = {
        customer: formData.customer,
        invoice_date: formData.invoice_date.format('YYYY-MM-DD'),
        due_date: formData.due_date.format('YYYY-MM-DD'),
        invoice_number: formData.invoice_number,
        po_number: formData.po_number,
        memo: formData.memo,
        message_to_customer: formData.message_to_customer,
        status,
        subtotal: formData.subtotal,
        tax_amount: formData.tax_amount,
        total_amount: formData.total_amount,
        line_items: formData.line_items.map(item => ({
          product: item.product,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          line_total: item.line_total,
          taxable: (item.tax_rate || 0) > 0,
          tax_rate: item.tax_rate || 0,
          tax_amount: item.tax_amount || 0,
        }))
      };

      // Call API to create invoice
      console.log('Creating invoice:', invoiceData);

      // Import and use the invoice service
      const { invoiceService } = await import('../../../services/invoice.service');
      await invoiceService.createInvoice(invoiceData);

      navigate('/dashboard/sales/invoices');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer);

  if (customersLoading || productsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/sales/invoices')}
          sx={{ mr: 2 }}
        >
          Back to Invoices
        </Button>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Create Invoice
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Single Column Layout - Full Width */}
      <Card>
        <CardContent>
          {/* Customer Selection */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={customers}
                getOptionLabel={(option) => option.display_name || option.name}
                value={selectedCustomer || null}
                onChange={(_, value) => handleCustomerChange(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Customer *"
                    placeholder="Select a customer"
                    required
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="P.O. Number"
                value={formData.po_number}
                onChange={(e) => setFormData(prev => ({ ...prev, po_number: e.target.value }))}
                fullWidth
              />
            </Grid>
          </Grid>

          {/* Invoice Details */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                label="Invoice Number"
                value={formData.invoice_number}
                onChange={(e) => setFormData(prev => ({ ...prev, invoice_number: e.target.value }))}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Invoice Date"
                value={formData.invoice_date}
                onChange={(date) => setFormData(prev => ({ ...prev, invoice_date: date || dayjs() }))}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Due Date"
                value={formData.due_date}
                onChange={(date) => setFormData(prev => ({ ...prev, due_date: date || dayjs().add(30, 'day') }))}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Line Items Table */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Items
          </Typography>

          <InvoiceLineTable
            lines={formData.line_items}
            products={products}
            onLineChange={handleLineChange}
            onAddLine={handleAddLine}
            onRemoveLine={handleRemoveLine}
            currencySymbol={companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
            defaultTaxRate={defaultTaxRate}
            showTaxColumn={companyInfo?.salesTaxRegistered || false}
          />

          {/* Summary Section - Bottom Right like invoice.png */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Box sx={{ width: 300 }}>
              {/* Subtotal */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, py: 1, borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="body1">Subtotal:</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                  {formData.subtotal.toFixed(2)}
                </Typography>
              </Box>

              {/* Sales Tax */}
              {companyInfo?.salesTaxRegistered && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, py: 1, borderBottom: '1px solid #e0e0e0' }}>
                  <Typography variant="body1">Sales Tax:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                    {formData.tax_amount.toFixed(2)}
                  </Typography>
                </Box>
              )}

              {/* Total */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 2, bgcolor: '#f5f5f5', px: 2, borderRadius: 1 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>Total:</Typography>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  {companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                  {formData.total_amount.toFixed(2)}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Notes */}
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Memo (Internal)"
                value={formData.memo}
                onChange={(e) => setFormData(prev => ({ ...prev, memo: e.target.value }))}
                multiline
                rows={3}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Message to Customer"
                value={formData.message_to_customer}
                onChange={(e) => setFormData(prev => ({ ...prev, message_to_customer: e.target.value }))}
                multiline
                rows={3}
                fullWidth
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={() => handleSave('draft')}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save as Draft'}
            </Button>

            <Button
              variant="contained"
              color="primary"
              startIcon={<SendIcon />}
              onClick={() => handleSave('sent')}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save & Send'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CreateInvoiceFormPage;
