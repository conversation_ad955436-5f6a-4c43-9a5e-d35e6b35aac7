import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Autocomplete,
  Divider,
  Alert,
  CircularProgress,
  Paper,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  SaveAlt as SaveCloseIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useCompany } from '../../../contexts/CompanyContext';
import { customerService, type CustomerOption } from '../../../services/customer.service';
import { InvoiceLineTable, CurrencyInput } from '../../../shared/components';
import { InvoiceLineItem } from '../../../shared/components/InvoiceLineTable';
import salesTaxService, { SalesTaxOption } from '../../../services/sales-tax.service';
import { v4 as uuidv4 } from 'uuid';

// Product interface for dropdown
interface ProductOption {
  id: number;
  name: string;
  sku?: string;
  description?: string;
  unit_price?: number;
  sales_price?: number;
  cost_price?: number;
}

interface InvoiceFormData {
  customer: number | null;
  invoice_date: Dayjs;
  due_date: Dayjs;
  invoice_number: string;
  po_number: string;
  memo: string;
  message_to_customer: string;
  line_items: InvoiceLineItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'sent';
}

const CreateInvoiceFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { companyInfo } = useCompany();

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [customersLoading, setCustomersLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [outputTaxes, setOutputTaxes] = useState<SalesTaxOption[]>([]);
  const [defaultTaxRate, setDefaultTaxRate] = useState(0);

  const [formData, setFormData] = useState<InvoiceFormData>({
    customer: null,
    invoice_date: dayjs(),
    due_date: dayjs().add(30, 'day'),
    invoice_number: '',
    po_number: '',
    memo: '',
    message_to_customer: '',
    line_items: [
      {
        id: uuidv4(),
        description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      }
    ],
    subtotal: 0,
    tax_amount: 0,
    total_amount: 0,
    status: 'draft',
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Check if token exists, if not set a default one for testing
        const token = localStorage.getItem('token');
        if (!token) {
          console.log('No token found, setting default token for testing');
          localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
        }
        // Load customers
        setCustomersLoading(true);
        const customerData = await customerService.getActiveCustomers();
        setCustomers(customerData);
        setCustomersLoading(false);

        // Load products
        setProductsLoading(true);
        const authToken = localStorage.getItem('token');
        const productResponse = await fetch('http://localhost:8000/api/sales/products/', {
          headers: {
            'Authorization': `Token ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        if (productResponse.ok) {
          const productData = await productResponse.json();
          setProducts(productData.results || productData || []);
        } else {
          console.warn('Failed to load products');
          setProducts([]);
        }
        setProductsLoading(false);

        // Load output taxes
        const taxes = await salesTaxService.getOutputTaxes();
        setOutputTaxes(taxes);

        // Set default tax rate from company settings or first tax rate
        if (companyInfo?.salesTaxRegistered && taxes.length > 0) {
          const defaultRate = taxes.find(tax => tax.description.toLowerCase().includes('standard'))?.rate || taxes[0].rate;
          setDefaultTaxRate(defaultRate);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load required data');
        setCustomersLoading(false);
        setProductsLoading(false);
      }
    };

    loadData();
  }, [companyInfo]);

  // Auto-generate invoice number
  useEffect(() => {
    if (!formData.invoice_number) {
      const invoiceNumber = `INV-${Date.now().toString().slice(-6)}`;
      setFormData(prev => ({ ...prev, invoice_number: invoiceNumber }));
    }
  }, []);

  // Calculate totals when line items change
  useEffect(() => {
    const subtotal = formData.line_items.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = formData.line_items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
    }));
  }, [formData.line_items]);

  // Handlers
  const handleCustomerChange = (customer: any) => {
    setFormData(prev => ({ ...prev, customer: customer?.contact || null }));
    
    // Auto-calculate due date based on customer payment terms
    if (customer?.payment_terms && formData.invoice_date) {
      const paymentTermsDays = parseInt(customer.payment_terms.replace(/\D/g, '')) || 30;
      const dueDate = formData.invoice_date.add(paymentTermsDays, 'day');
      setFormData(prev => ({ ...prev, due_date: dueDate }));
    }
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.map(item =>
        item.id === lineId ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleAddLine = () => {
    const newLine: InvoiceLineItem = {
      id: uuidv4(),
      description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      tax_rate: defaultTaxRate,
      tax_amount: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, newLine]
    }));
  };

  const handleRemoveLine = (lineId: string) => {
    if (formData.line_items.length > 1) {
      setFormData(prev => ({
        ...prev,
        line_items: prev.line_items.filter(item => item.id !== lineId)
      }));
    }
  };

  const handleSave = async (status: 'draft' | 'sent', action: 'save' | 'save_close' | 'save_new' = 'save') => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.customer) {
        throw new Error('Please select a customer');
      }

      if (formData.line_items.length === 0 || formData.line_items.every(item => !item.description)) {
        throw new Error('Please add at least one line item');
      }

      // Create invoice with line_items
      const invoiceData = {
        customer: formData.customer,
        invoice_date: formData.invoice_date.format('YYYY-MM-DD'),
        due_date: formData.due_date.format('YYYY-MM-DD'),
        status,
        po_number: formData.po_number,
        memo: formData.memo,
        message_to_customer: formData.message_to_customer,
        subtotal: formData.subtotal,
        tax_amount: formData.tax_amount,
        total_amount: formData.total_amount,
        line_items: formData.line_items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          line_total: item.line_total,
          taxable: (item.tax_rate || 0) > 0,
          tax_rate: item.tax_rate || 0,
          tax_amount: item.tax_amount || 0
        }))
      };

      // Call API to create invoice
      console.log('Creating invoice:', invoiceData);

      // Create invoice directly with fetch to avoid serializer issues
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/sales/invoices/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create invoice: ${JSON.stringify(errorData)}`);
      }

      const createdInvoice = await response.json();

      // Handle different actions after save
      if (action === 'save_close') {
        navigate('/dashboard/sales/invoices');
      } else if (action === 'save_new') {
        // Reset form for new invoice
        window.location.reload();
      } else {
        // Just save - stay on current page
        console.log('Invoice saved successfully');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const selectedCustomer = customers.find(c => c.contact === formData.customer);

  if (customersLoading || productsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      bgcolor: '#f5f5f5',
      zIndex: 1300,
      overflow: 'auto'
    }}>
      {/* Header */}
      <Box sx={{
        bgcolor: 'white',
        borderBottom: '1px solid #e0e0e0',
        px: 3,
        py: 2,
        display: 'flex',
        alignItems: 'center',
        position: 'sticky',
        top: 0,
        zIndex: 100
      }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/sales/invoices')}
          sx={{ mr: 2 }}
        >
          Back to Invoices
        </Button>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Create Invoice
        </Typography>
      </Box>

      {/* Main Content */}
      <Box sx={{ p: 3, maxWidth: '1600px', mx: 'auto' }}>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Single Column Layout - Full Width */}
        <Card sx={{
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          borderRadius: 2,
          overflow: 'hidden'
        }}>
          <CardContent sx={{ p: 4 }}>
          {/* Customer Selection */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={customers}
                getOptionLabel={(option) => option.display_name || option.name}
                value={selectedCustomer || null}
                onChange={(_, value) => handleCustomerChange(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Customer *"
                    placeholder="Select a customer"
                    required
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="P.O. Number"
                value={formData.po_number}
                onChange={(e) => setFormData(prev => ({ ...prev, po_number: e.target.value }))}
                fullWidth
              />
            </Grid>
          </Grid>

          {/* Invoice Details */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                label="Invoice Number"
                value={formData.invoice_number}
                onChange={(e) => setFormData(prev => ({ ...prev, invoice_number: e.target.value }))}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Invoice Date"
                value={formData.invoice_date}
                onChange={(date) => setFormData(prev => ({ ...prev, invoice_date: date || dayjs() }))}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Due Date"
                value={formData.due_date}
                onChange={(date) => setFormData(prev => ({ ...prev, due_date: date || dayjs().add(30, 'day') }))}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Line Items Table */}
          <Typography variant="h6" sx={{ mb: 3, color: '#2c3e50', fontWeight: 600 }}>
            Invoice Items
          </Typography>

          <InvoiceLineTable
            lines={formData.line_items}
            products={products}
            onLineChange={handleLineChange}
            onAddLine={handleAddLine}
            onRemoveLine={handleRemoveLine}
            currencySymbol={companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
            defaultTaxRate={defaultTaxRate}
            showTaxColumn={true}
            showDiscountColumn={false}
          />

          {/* Summary Section - Bottom Right like invoice.png */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Box sx={{ width: 350 }}>
              {/* Subtotal */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, py: 1, borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="body1" sx={{ minWidth: 120 }}>Subtotal:</Typography>
                <Box sx={{ width: 150 }}>
                  <CurrencyInput
                    value={formData.subtotal}
                    onChange={(value) => setFormData(prev => ({ ...prev, subtotal: value }))}
                    currencySymbol={companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                    size="small"
                    disabled
                  />
                </Box>
              </Box>

              {/* Sales Tax */}
              {true && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, py: 1, borderBottom: '1px solid #e0e0e0' }}>
                  <Typography variant="body1" sx={{ minWidth: 120 }}>Sales Tax:</Typography>
                  <Box sx={{ width: 150 }}>
                    <CurrencyInput
                      value={formData.tax_amount}
                      onChange={(value) => setFormData(prev => ({ ...prev, tax_amount: value }))}
                      currencySymbol={companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                      size="small"
                      disabled
                    />
                  </Box>
                </Box>
              )}

              {/* Total */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 2, bgcolor: '#f5f5f5', px: 2, borderRadius: 1 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, minWidth: 120 }}>Total:</Typography>
                <Box sx={{ width: 150 }}>
                  <CurrencyInput
                    value={formData.total_amount}
                    onChange={(value) => setFormData(prev => ({ ...prev, total_amount: value }))}
                    currencySymbol={companyInfo?.functionalCurrency === 'USD' ? '$' : companyInfo?.functionalCurrency || '$'}
                    size="small"
                    disabled
                    sx={{
                      '& .MuiInputBase-input': {
                        fontWeight: 600,
                        color: 'primary.main'
                      }
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Notes */}
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Memo (Internal)"
                value={formData.memo}
                onChange={(e) => setFormData(prev => ({ ...prev, memo: e.target.value }))}
                multiline
                rows={3}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Message to Customer"
                value={formData.message_to_customer}
                onChange={(e) => setFormData(prev => ({ ...prev, message_to_customer: e.target.value }))}
                multiline
                rows={3}
                fullWidth
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={() => handleSave('draft', 'save')}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<SaveCloseIcon />}
              onClick={() => handleSave('draft', 'save_close')}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save & Close'}
            </Button>

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleSave('draft', 'save_new')}
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save & New'}
            </Button>
          </Box>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default CreateInvoiceFormPage;
