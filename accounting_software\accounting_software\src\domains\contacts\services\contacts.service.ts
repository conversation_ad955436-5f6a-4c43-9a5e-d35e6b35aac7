import api from '../../../services/api';
import {
  Contact,
  Customer,
  Vendor,
  Employee,
  ContactFormData,
  CustomerFormData,
  VendorFormData,
  EmployeeFormData,
  ContactsApiResponse,
  CustomersApiResponse,
  VendorsApiResponse,
  EmployeesApiResponse
} from '../types/contacts.types';

const CONTACTS_BASE_URL = '/contacts';

export class ContactsService {
  // Contacts CRUD
  static async getContacts(params?: Record<string, any>): Promise<ContactsApiResponse> {
    const response = await api.get(`${CONTACTS_BASE_URL}/contacts/`, { params });
    return response.data;
  }

  static async getContact(id: number): Promise<Contact> {
    const response = await api.get(`${CONTACTS_BASE_URL}/contacts/${id}/`);
    return response.data;
  }

  static async createContact(data: ContactFormData): Promise<Contact> {
    const response = await api.post(`${CONTACTS_BASE_URL}/contacts/`, data);
    return response.data;
  }

  static async updateContact(id: number, data: Partial<ContactFormData>): Promise<Contact> {
    const response = await api.patch(`${CONTACTS_BASE_URL}/contacts/${id}/`, data);
    return response.data;
  }

  static async deleteContact(id: number): Promise<void> {
    await api.delete(`${CONTACTS_BASE_URL}/contacts/${id}/`);
  }

  // Customers CRUD
  static async getCustomers(params?: Record<string, any>): Promise<CustomersApiResponse> {
    const response = await api.get(`${CONTACTS_BASE_URL}/customers/`, { params });
    return response.data;
  }

  static async getCustomer(id: number): Promise<Customer> {
    const response = await api.get(`${CONTACTS_BASE_URL}/customers/${id}/`);
    return response.data;
  }

  static async createCustomer(data: CustomerFormData): Promise<Customer> {
    // First create the contact
    const contactData = {
      name: data.displayName,
      email: data.email,
      phone: data.phone,
      contact_type: 'customer'
    };

    const contactResponse = await api.post(`${CONTACTS_BASE_URL}/contacts/`, contactData);
    const contact = contactResponse.data;

    // Then create the customer with the contact ID
    const customerData = {
      contact: contact.id,
      customer_code: data.customerCode,
      credit_limit: data.creditLimit,
      payment_terms: data.paymentTerms,
      customer_category: data.customerCategory,
      discount_percentage: data.discountPercentage,
      tax_exempt: data.taxExempt || false,
      first_name: data.firstName,
      last_name: data.lastName,
      company_name: data.companyName,
      mobile: data.mobile,
      billing_street: data.billingStreet,
      billing_city: data.billingCity,
      billing_state: data.billingState,
      billing_postal_code: data.billingPostalCode,
      billing_country: data.billingCountry,
      shipping_same_as_billing: data.shippingSameAsBilling,
      shipping_street: data.shippingStreet,
      shipping_city: data.shippingCity,
      shipping_state: data.shippingState,
      shipping_postal_code: data.shippingPostalCode,
      shipping_country: data.shippingCountry
    };

    const response = await api.post(`${CONTACTS_BASE_URL}/customers/`, customerData);
    return response.data;
  }

  static async updateCustomer(id: number, data: Partial<CustomerFormData>): Promise<Customer> {
    const response = await api.patch(`${CONTACTS_BASE_URL}/customers/${id}/`, data);
    return response.data;
  }

  static async deleteCustomer(id: number): Promise<void> {
    await api.delete(`${CONTACTS_BASE_URL}/customers/${id}/`);
  }

  // Vendors CRUD
  static async getVendors(params?: Record<string, any>): Promise<VendorsApiResponse> {
    const response = await api.get(`${CONTACTS_BASE_URL}/vendors/`, { params });
    return response.data;
  }

  static async getVendor(id: number): Promise<Vendor> {
    const response = await api.get(`${CONTACTS_BASE_URL}/vendors/${id}/`);
    return response.data;
  }

  static async createVendor(data: VendorFormData): Promise<Vendor> {
    const response = await api.post(`${CONTACTS_BASE_URL}/vendors/`, data);
    return response.data;
  }

  static async updateVendor(id: number, data: Partial<VendorFormData>): Promise<Vendor> {
    const response = await api.patch(`${CONTACTS_BASE_URL}/vendors/${id}/`, data);
    return response.data;
  }

  static async deleteVendor(id: number): Promise<void> {
    await api.delete(`${CONTACTS_BASE_URL}/vendors/${id}/`);
  }

  // Employees CRUD
  static async getEmployees(params?: Record<string, any>): Promise<EmployeesApiResponse> {
    const response = await api.get(`${CONTACTS_BASE_URL}/employees/`, { params });
    return response.data;
  }

  static async getEmployee(id: number): Promise<Employee> {
    const response = await api.get(`${CONTACTS_BASE_URL}/employees/${id}/`);
    return response.data;
  }

  static async createEmployee(data: EmployeeFormData): Promise<Employee> {
    const response = await api.post(`${CONTACTS_BASE_URL}/employees/`, data);
    return response.data;
  }

  static async updateEmployee(id: number, data: Partial<EmployeeFormData>): Promise<Employee> {
    const response = await api.patch(`${CONTACTS_BASE_URL}/employees/${id}/`, data);
    return response.data;
  }

  static async deleteEmployee(id: number): Promise<void> {
    await api.delete(`${CONTACTS_BASE_URL}/employees/${id}/`);
  }

  // Utility methods
  static async searchContacts(query: string, contactType?: string): Promise<Contact[]> {
    const params = { search: query };
    if (contactType) {
      (params as any).contact_type = contactType;
    }
    const response = await api.get(`${CONTACTS_BASE_URL}/contacts/`, { params });
    return response.data.results || response.data;
  }

  static async getContactsByType(contactType: 'customer' | 'vendor' | 'employee' | 'other'): Promise<Contact[]> {
    const response = await api.get(`${CONTACTS_BASE_URL}/contacts/`, { params: { contact_type: contactType } });
    return response.data.results || response.data;
  }

  static async bulkCreateContacts(contacts: ContactFormData[]): Promise<Contact[]> {
    const promises = contacts.map(contact => this.createContact(contact));
    return Promise.all(promises);
  }
}

export default ContactsService; 