from rest_framework import serializers
from .models import PriceList, PriceListItem, DiscountRule

class PriceListSerializer(serializers.ModelSerializer):
    class Meta:
        model = PriceList
        fields = ['id', 'name', 'description', 'currency', 'valid_from', 
                 'valid_to', 'is_active', 'is_default']
        read_only_fields = ['id']

class PriceListItemSerializer(serializers.ModelSerializer):
    product_code = serializers.CharField(source='product.code', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    uom = serializers.CharField(source='product.uom', read_only=True)
    
    class Meta:
        model = PriceListItem
        fields = ['id', 'price_list', 'product', 'product_code', 'product_name',
                 'uom', 'unit_price', 'min_quantity', 'discount_percent',
                 'effective_date', 'expiry_date']
        read_only_fields = ['id']

class DiscountRuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = DiscountRule
        fields = ['id', 'name', 'description', 'discount_percent', 'min_quantity',
                 'customer_group', 'product_category', 'start_date', 'end_date',
                 'is_active']
        read_only_fields = ['id']