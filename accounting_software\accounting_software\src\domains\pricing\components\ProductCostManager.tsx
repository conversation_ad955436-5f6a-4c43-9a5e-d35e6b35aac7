// src/components/pricing/ProductCostManager.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tabs,
  Tab
} from '@mui/material';
import { Edit as EditIcon, TrendingUp as TrendingUpIcon, Assessment as AssessmentIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, ProductCost } from '../../../services/pricingService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`cost-tabpanel-${index}`}
      aria-labelledby={`cost-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ProductCostManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [products, setProducts] = useState<ProductCost[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<ProductCost | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    cost_method: 'standard' as 'standard' | 'average' | 'fifo',
    standard_cost: 0,
    average_cost: 0,
    last_cost: 0
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getProductCosts();
      setProducts(response.data);
    } catch (error) {
      enqueueSnackbar('Failed to fetch product costs', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (product: ProductCost) => {
    setCurrentProduct(product);
    setFormData({
      cost_method: product.cost_method,
      standard_cost: product.standard_cost,
      average_cost: product.average_cost,
      last_cost: product.last_cost
    });
    setDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!currentProduct) return;

    try {
      await pricingService.updateProductCost(currentProduct.id, formData);
      enqueueSnackbar('Product cost updated successfully', { variant: 'success' });
      setDialogOpen(false);
      fetchProducts();
    } catch (error) {
      enqueueSnackbar('Failed to update product cost', { variant: 'error' });
    }
  };

  const calculateMargin = (salePrice: number, cost: number) => {
    if (cost === 0) return 0;
    return ((salePrice - cost) / salePrice) * 100;
  };

  const getCostByMethod = (product: ProductCost) => {
    switch (product.cost_method) {
      case 'standard':
        return product.standard_cost;
      case 'average':
        return product.average_cost;
      case 'fifo':
        return product.last_cost;
      default:
        return product.standard_cost;
    }
  };

  const costColumns = [
    {
      field: 'code',
      headerName: 'Product Code',
      width: 120,
    },
    {
      field: 'name',
      headerName: 'Product Name',
      flex: 1,
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 100,
      renderCell: (params: any) => (
        <Chip 
          label={params.value} 
          color={params.value === 'product' ? 'primary' : 'secondary'} 
          size="small" 
        />
      ),
    },
    {
      field: 'cost_method',
      headerName: 'Cost Method',
      width: 120,
      renderCell: (params: any) => (
        <Chip label={params.value.toUpperCase()} variant="outlined" size="small" />
      ),
    },
    {
      field: 'standard_cost',
      headerName: 'Standard Cost',
      width: 120,
      renderCell: (params: any) => `$${params.value.toFixed(2)}`,
    },
    {
      field: 'average_cost',
      headerName: 'Average Cost',
      width: 120,
      renderCell: (params: any) => `$${params.value.toFixed(2)}`,
    },
    {
      field: 'last_cost',
      headerName: 'Last Cost',
      width: 120,
      renderCell: (params: any) => `$${params.value.toFixed(2)}`,
    },
    {
      field: 'current_cost',
      headerName: 'Current Cost',
      width: 120,
      renderCell: (params: any) => {
        const cost = getCostByMethod(params.row);
        return (
          <Typography variant="body2" fontWeight="bold" color="primary">
            ${cost.toFixed(2)}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      renderCell: (params: any) => (
        <IconButton 
          size="small" 
          onClick={() => handleEdit(params.row)}
          color="primary"
        >
          <EditIcon />
        </IconButton>
      ),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="Product Costs" icon={<TrendingUpIcon />} />
            <Tab label="Cost Analysis" icon={<AssessmentIcon />} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5">Product Cost Management</Typography>
          </Box>

          <DataTable
            columns={costColumns}
            rows={products}
            loading={loading}
            pageSize={10}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h5" gutterBottom>
            Cost Analysis
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Cost analysis features will be implemented here, including:
          </Typography>
          <Box component="ul" sx={{ mt: 2 }}>
            <li>Cost trends over time</li>
            <li>Cost variance analysis</li>
            <li>Margin analysis by product</li>
            <li>Cost method comparison</li>
          </Box>
        </TabPanel>

        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Edit Product Cost - {currentProduct?.code} {currentProduct?.name}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Cost Method</InputLabel>
                  <Select
                    value={formData.cost_method}
                    label="Cost Method"
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      cost_method: e.target.value as 'standard' | 'average' | 'fifo' 
                    }))}
                  >
                    <MenuItem value="standard">Standard Cost</MenuItem>
                    <MenuItem value="average">Average Cost</MenuItem>
                    <MenuItem value="fifo">FIFO (Last Cost)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Standard Cost"
                  type="number"
                  value={formData.standard_cost}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    standard_cost: parseFloat(e.target.value) || 0 
                  }))}
                  inputProps={{ min: 0, step: 0.01 }}
                  helperText="Predetermined cost for planning"
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Average Cost"
                  type="number"
                  value={formData.average_cost}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    average_cost: parseFloat(e.target.value) || 0 
                  }))}
                  inputProps={{ min: 0, step: 0.01 }}
                  helperText="Calculated average cost"
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Last Cost"
                  type="number"
                  value={formData.last_cost}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    last_cost: parseFloat(e.target.value) || 0 
                  }))}
                  inputProps={{ min: 0, step: 0.01 }}
                  helperText="Most recent purchase cost"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              Update Cost
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default ProductCostManager;
