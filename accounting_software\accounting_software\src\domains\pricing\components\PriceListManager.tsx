// src/components/pricing/PriceListManager.tsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, DatePicker, Select, message, Card, Tag } from 'antd';
import { pricingService, PriceList } from '../../../services/pricingService';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

const PriceListManager: React.FC = () => {
  const [priceLists, setPriceLists] = useState<PriceList[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);

  const currencyOptions = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
    { value: 'JPY', label: 'Japanese Yen (¥)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'AUD', label: 'Australian Dollar (A$)' },
    { value: 'INR', label: 'Indian Rupee (₹)' },
    { value: 'CNY', label: 'Chinese Yuan (¥)' },
    { value: 'NGN', label: 'Nigerian Naira (₦)' },
    { value: 'ZAR', label: 'South African Rand (R)' },
  ];

  useEffect(() => {
    fetchPriceLists();
  }, []);

  const fetchPriceLists = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getPriceLists();
      setPriceLists(response.data);
    } catch (error) {
      message.error('Failed to fetch price lists');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record: PriceList) => {
    form.setFieldsValue({
      ...record,
      valid_from: record.valid_from ? dayjs(record.valid_from) : null,
      valid_to: record.valid_to ? dayjs(record.valid_to) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await pricingService.deletePriceList(id);
      message.success('Price list deleted successfully');
      fetchPriceLists();
    } catch (error) {
      message.error('Failed to delete price list');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const data = {
        ...values,
        valid_from: values.valid_from ? values.valid_from.format('YYYY-MM-DD') : null,
        valid_to: values.valid_to ? values.valid_to.format('YYYY-MM-DD') : null,
      };

      if (editMode && currentId) {
        await pricingService.updatePriceList(currentId, data);
        message.success('Price list updated successfully');
      } else {
        await pricingService.createPriceList(data);
        message.success('Price list created successfully');
      }

      setModalVisible(false);
      fetchPriceLists();
    } catch (error) {
      message.error('Failed to save price list');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => (
        <Tag color="blue">{currency}</Tag>
      ),
    },
    {
      title: 'Valid From',
      dataIndex: 'valid_from',
      key: 'valid_from',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: 'Valid To',
      dataIndex: 'valid_to',
      key: 'valid_to',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : 'No Expiry',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Default',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) => (
        isDefault ? <Tag color="gold">Default</Tag> : null
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: PriceList) => (
        <div>
          <Button 
            type="link" 
            onClick={() => handleEdit(record)}
            style={{ padding: 0, marginRight: 8 }}
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => handleDelete(record.id)}
            style={{ padding: 0 }}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Card title="Price Lists Management">
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleCreate}>
          Create Price List
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={priceLists}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editMode ? 'Edit Price List' : 'Create Price List'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter price list name' }]}
          >
            <Input placeholder="Enter price list name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={3} placeholder="Enter description" />
          </Form.Item>

          <Form.Item
            name="currency"
            label="Currency"
            rules={[{ required: true, message: 'Please select currency' }]}
          >
            <Select placeholder="Select currency">
              {currencyOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="valid_from"
            label="Valid From"
            rules={[{ required: true, message: 'Please select valid from date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="valid_to"
            label="Valid To"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
            initialValue={true}
          >
            <Select>
              <Option value={true}>Active</Option>
              <Option value={false}>Inactive</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="is_default"
            label="Set as Default"
            valuePropName="checked"
            initialValue={false}
          >
            <Select>
              <Option value={true}>Yes</Option>
              <Option value={false}>No</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default PriceListManager;
