// src/components/pricing/PriceListManager.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Grid
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, PriceList } from '../../../services/pricingService';
import dayjs from 'dayjs';

const PriceListManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [priceLists, setPriceLists] = useState<PriceList[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    currency: 'USD',
    valid_from: dayjs(),
    valid_to: null as dayjs.Dayjs | null,
    is_active: true,
    is_default: false
  });

  const currencyOptions = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
    { value: 'JPY', label: 'Japanese Yen (¥)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'AUD', label: 'Australian Dollar (A$)' },
    { value: 'INR', label: 'Indian Rupee (₹)' },
    { value: 'CNY', label: 'Chinese Yuan (¥)' },
    { value: 'NGN', label: 'Nigerian Naira (₦)' },
    { value: 'ZAR', label: 'South African Rand (R)' },
  ];

  useEffect(() => {
    fetchPriceLists();
  }, []);

  const fetchPriceLists = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getPriceLists();
      setPriceLists(response.data);
    } catch (error) {
      enqueueSnackbar('Failed to fetch price lists', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setFormData({
      name: '',
      description: '',
      currency: 'USD',
      valid_from: dayjs(),
      valid_to: null,
      is_active: true,
      is_default: false
    });
    setEditMode(false);
    setCurrentId(null);
    setDialogOpen(true);
  };

  const handleEdit = (record: PriceList) => {
    setFormData({
      name: record.name,
      description: record.description || '',
      currency: record.currency,
      valid_from: record.valid_from ? dayjs(record.valid_from) : dayjs(),
      valid_to: record.valid_to ? dayjs(record.valid_to) : null,
      is_active: record.is_active,
      is_default: record.is_default
    });
    setEditMode(true);
    setCurrentId(record.id);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this price list?')) {
      try {
        await pricingService.deletePriceList(id);
        enqueueSnackbar('Price list deleted successfully', { variant: 'success' });
        fetchPriceLists();
      } catch (error) {
        enqueueSnackbar('Failed to delete price list', { variant: 'error' });
      }
    }
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      enqueueSnackbar('Please enter a price list name', { variant: 'warning' });
      return;
    }

    try {
      const data = {
        name: formData.name,
        description: formData.description,
        currency: formData.currency,
        valid_from: formData.valid_from.format('YYYY-MM-DD'),
        valid_to: formData.valid_to ? formData.valid_to.format('YYYY-MM-DD') : null,
        is_active: formData.is_active,
        is_default: formData.is_default
      };

      if (editMode && currentId) {
        await pricingService.updatePriceList(currentId, data);
        enqueueSnackbar('Price list updated successfully', { variant: 'success' });
      } else {
        await pricingService.createPriceList(data);
        enqueueSnackbar('Price list created successfully', { variant: 'success' });
      }

      setDialogOpen(false);
      fetchPriceLists();
    } catch (error) {
      enqueueSnackbar('Failed to save price list', { variant: 'error' });
    }
  };

  const columns = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
    },
    {
      field: 'currency',
      headerName: 'Currency',
      width: 120,
      renderCell: (params: any) => (
        <Chip label={params.value} color="primary" size="small" />
      ),
    },
    {
      field: 'valid_from',
      headerName: 'Valid From',
      width: 120,
      renderCell: (params: any) => 
        params.value ? dayjs(params.value).format('YYYY-MM-DD') : '-',
    },
    {
      field: 'valid_to',
      headerName: 'Valid To',
      width: 120,
      renderCell: (params: any) => 
        params.value ? dayjs(params.value).format('YYYY-MM-DD') : 'No Expiry',
    },
    {
      field: 'is_active',
      headerName: 'Status',
      width: 100,
      renderCell: (params: any) => (
        <Chip 
          label={params.value ? 'Active' : 'Inactive'} 
          color={params.value ? 'success' : 'error'} 
          size="small" 
        />
      ),
    },
    {
      field: 'is_default',
      headerName: 'Default',
      width: 100,
      renderCell: (params: any) => 
        params.value ? <Chip label="Default" color="warning" size="small" /> : null,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: any) => (
        <Box>
          <IconButton 
            size="small" 
            onClick={() => handleEdit(params.row)}
            color="primary"
          >
            <EditIcon />
          </IconButton>
          <IconButton 
            size="small" 
            onClick={() => handleDelete(params.row.id)}
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Price Lists Management</Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleCreate}
          >
            Create Price List
          </Button>
        </Box>

        <DataTable
          columns={columns}
          rows={priceLists}
          loading={loading}
          pageSize={10}
        />

        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editMode ? 'Edit Price List' : 'Create Price List'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Currency</InputLabel>
                  <Select
                    value={formData.currency}
                    label="Currency"
                    onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                  >
                    {currencyOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Valid From"
                  value={formData.valid_from}
                  onChange={(date) => setFormData(prev => ({ ...prev, valid_from: date || dayjs() }))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Valid To"
                  value={formData.valid_to}
                  onChange={(date) => setFormData(prev => ({ ...prev, valid_to: date }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    />
                  }
                  label="Active"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_default}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_default: e.target.checked }))}
                    />
                  }
                  label="Set as Default Price List"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              {editMode ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default PriceListManager;
