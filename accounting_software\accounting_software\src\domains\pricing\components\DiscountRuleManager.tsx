// src/components/pricing/DiscountRuleManager.tsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, InputNumber, DatePicker, Select, message, Card, Tag } from 'antd';
import { pricingService, DiscountRule } from '../../../services/pricingService';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

const DiscountRuleManager: React.FC = () => {
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);

  useEffect(() => {
    fetchDiscountRules();
  }, []);

  const fetchDiscountRules = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getDiscountRules();
      setDiscountRules(response.data);
    } catch (error) {
      message.error('Failed to fetch discount rules');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record: DiscountRule) => {
    form.setFieldsValue({
      ...record,
      start_date: record.start_date ? dayjs(record.start_date) : null,
      end_date: record.end_date ? dayjs(record.end_date) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await pricingService.deleteDiscountRule(id);
      message.success('Discount rule deleted successfully');
      fetchDiscountRules();
    } catch (error) {
      message.error('Failed to delete discount rule');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const data = {
        ...values,
        start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : null,
        end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : null,
      };

      if (editMode && currentId) {
        await pricingService.updateDiscountRule(currentId, data);
        message.success('Discount rule updated successfully');
      } else {
        await pricingService.createDiscountRule(data);
        message.success('Discount rule created successfully');
      }

      setModalVisible(false);
      fetchDiscountRules();
    } catch (error) {
      message.error('Failed to save discount rule');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Discount %',
      dataIndex: 'discount_percent',
      key: 'discount_percent',
      render: (percent: number) => `${percent}%`,
    },
    {
      title: 'Min Quantity',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
      render: (qty: number) => qty || '-',
    },
    {
      title: 'Customer Group',
      dataIndex: 'customer_group',
      key: 'customer_group',
      render: (group: string) => group || '-',
    },
    {
      title: 'Start Date',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'End Date',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : 'No End',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: DiscountRule) => (
        <div>
          <Button 
            type="link" 
            onClick={() => handleEdit(record)}
            style={{ padding: 0, marginRight: 8 }}
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => handleDelete(record.id)}
            style={{ padding: 0 }}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Card title="Discount Rules Management">
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleCreate}>
          Create Discount Rule
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={discountRules}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editMode ? 'Edit Discount Rule' : 'Create Discount Rule'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Rule Name"
            rules={[{ required: true, message: 'Please enter rule name' }]}
          >
            <Input placeholder="Enter rule name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={3} placeholder="Enter description" />
          </Form.Item>

          <Form.Item
            name="discount_percent"
            label="Discount Percentage"
            rules={[{ required: true, message: 'Please enter discount percentage' }]}
          >
            <InputNumber
              min={0}
              max={100}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="Enter discount percentage"
            />
          </Form.Item>

          <Form.Item
            name="min_quantity"
            label="Minimum Quantity"
          >
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="Enter minimum quantity"
            />
          </Form.Item>

          <Form.Item
            name="customer_group"
            label="Customer Group"
          >
            <Input placeholder="Enter customer group" />
          </Form.Item>

          <Form.Item
            name="start_date"
            label="Start Date"
            rules={[{ required: true, message: 'Please select start date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="end_date"
            label="End Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="Status"
            initialValue={true}
          >
            <Select>
              <Option value={true}>Active</Option>
              <Option value={false}>Inactive</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default DiscountRuleManager;
