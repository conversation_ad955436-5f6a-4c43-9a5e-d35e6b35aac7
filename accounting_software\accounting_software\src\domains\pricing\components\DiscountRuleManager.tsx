// src/components/pricing/DiscountRuleManager.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Grid
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, DiscountRule } from '../../../services/pricingService';
import dayjs from 'dayjs';

const DiscountRuleManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    discount_percent: 0,
    min_quantity: 0,
    customer_group: '',
    start_date: dayjs(),
    end_date: null as dayjs.Dayjs | null,
    is_active: true
  });

  useEffect(() => {
    fetchDiscountRules();
  }, []);

  const fetchDiscountRules = async () => {
    setLoading(true);
    try {
      // Ensure token is set
      if (!localStorage.getItem('token')) {
        localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
      }

      const response = await pricingService.getDiscountRules();
      setDiscountRules((response.data as any)?.results || response.data || []);
    } catch (error) {
      enqueueSnackbar('Failed to fetch discount rules', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setFormData({
      name: '',
      description: '',
      discount_percent: 0,
      min_quantity: 0,
      customer_group: '',
      start_date: dayjs(),
      end_date: null,
      is_active: true
    });
    setEditMode(false);
    setCurrentId(null);
    setDialogOpen(true);
  };

  const handleEdit = (record: DiscountRule) => {
    setFormData({
      name: record.name,
      description: record.description || '',
      discount_percent: record.discount_percent,
      min_quantity: record.min_quantity || 0,
      customer_group: record.customer_group || '',
      start_date: record.start_date ? dayjs(record.start_date) : dayjs(),
      end_date: record.end_date ? dayjs(record.end_date) : null,
      is_active: record.is_active
    });
    setEditMode(true);
    setCurrentId(record.id);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this discount rule?')) {
      try {
        await pricingService.deleteDiscountRule(id);
        enqueueSnackbar('Discount rule deleted successfully', { variant: 'success' });
        fetchDiscountRules();
      } catch (error) {
        enqueueSnackbar('Failed to delete discount rule', { variant: 'error' });
      }
    }
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      enqueueSnackbar('Please enter a rule name', { variant: 'warning' });
      return;
    }

    try {
      const data = {
        name: formData.name,
        description: formData.description,
        discount_percent: formData.discount_percent,
        min_quantity: formData.min_quantity || undefined,
        customer_group: formData.customer_group || undefined,
        start_date: formData.start_date.format('YYYY-MM-DD'),
        end_date: formData.end_date ? formData.end_date.format('YYYY-MM-DD') : undefined,
        is_active: formData.is_active
      };

      if (editMode && currentId) {
        await pricingService.updateDiscountRule(currentId, data);
        enqueueSnackbar('Discount rule updated successfully', { variant: 'success' });
      } else {
        await pricingService.createDiscountRule(data);
        enqueueSnackbar('Discount rule created successfully', { variant: 'success' });
      }

      setDialogOpen(false);
      fetchDiscountRules();
    } catch (error) {
      enqueueSnackbar('Failed to save discount rule', { variant: 'error' });
    }
  };

  const columns = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
    },
    {
      field: 'discount_percent',
      headerName: 'Discount %',
      width: 120,
      renderCell: (params: any) => `${params.value}%`,
    },
    {
      field: 'min_quantity',
      headerName: 'Min Quantity',
      width: 120,
      renderCell: (params: any) => params.value || '-',
    },
    {
      field: 'customer_group',
      headerName: 'Customer Group',
      width: 150,
      renderCell: (params: any) => params.value || '-',
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
      width: 120,
      renderCell: (params: any) => dayjs(params.value).format('YYYY-MM-DD'),
    },
    {
      field: 'end_date',
      headerName: 'End Date',
      width: 120,
      renderCell: (params: any) => 
        params.value ? dayjs(params.value).format('YYYY-MM-DD') : 'No End',
    },
    {
      field: 'is_active',
      headerName: 'Status',
      width: 100,
      renderCell: (params: any) => (
        <Chip 
          label={params.value ? 'Active' : 'Inactive'} 
          color={params.value ? 'success' : 'error'} 
          size="small" 
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: any) => (
        <Box>
          <IconButton 
            size="small" 
            onClick={() => handleEdit(params.row)}
            color="primary"
          >
            <EditIcon />
          </IconButton>
          <IconButton 
            size="small" 
            onClick={() => handleDelete(params.row.id)}
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Discount Rules Management</Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleCreate}
          >
            Create Discount Rule
          </Button>
        </Box>

        <DataTable
          columns={columns}
          rows={discountRules}
          loading={loading}
          pageSize={10}
        />

        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editMode ? 'Edit Discount Rule' : 'Create Discount Rule'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Rule Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Discount Percentage"
                  type="number"
                  value={formData.discount_percent}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percent: parseFloat(e.target.value) || 0 }))}
                  inputProps={{ min: 0, max: 100, step: 0.01 }}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Minimum Quantity"
                  type="number"
                  value={formData.min_quantity}
                  onChange={(e) => setFormData(prev => ({ ...prev, min_quantity: parseFloat(e.target.value) || 0 }))}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Customer Group"
                  value={formData.customer_group}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_group: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Start Date"
                  value={formData.start_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, start_date: date || dayjs() }))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="End Date"
                  value={formData.end_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, end_date: date }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    />
                  }
                  label="Active"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              {editMode ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default DiscountRuleManager;
