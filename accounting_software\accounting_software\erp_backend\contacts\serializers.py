from rest_framework import serializers
from .models import (
    Contact, Customer, Vendor, Employee
)


class ContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contact
        fields = '__all__'


class CustomerSerializer(serializers.ModelSerializer):
    contact_name = serializers.Char<PERSON>ield(source='contact.name', read_only=True)
    contact_email = serializers.CharField(source='contact.email', read_only=True)
    contact_phone = serializers.CharField(source='contact.phone', read_only=True)
    contact_type = serializers.CharField(source='contact.contact_type', read_only=True)
    
    class Meta:
        model = Customer
        fields = '__all__'


class VendorSerializer(serializers.ModelSerializer):
    contact_name = serializers.Char<PERSON><PERSON>(source='contact.name', read_only=True)
    contact_email = serializers.CharField(source='contact.email', read_only=True)
    contact_phone = serializers.Char<PERSON>ield(source='contact.phone', read_only=True)
    contact_type = serializers.Char<PERSON><PERSON>(source='contact.contact_type', read_only=True)
    
    class Meta:
        model = Vendor
        fields = '__all__'


class EmployeeSerializer(serializers.ModelSerializer):
    contact_name = serializers.Char<PERSON><PERSON>(source='contact.name', read_only=True)
    contact_email = serializers.Char<PERSON>ield(source='contact.email', read_only=True)
    contact_phone = serializers.CharField(source='contact.phone', read_only=True)
    contact_type = serializers.CharField(source='contact.contact_type', read_only=True)
    
    class Meta:
        model = Employee
        fields = '__all__' 