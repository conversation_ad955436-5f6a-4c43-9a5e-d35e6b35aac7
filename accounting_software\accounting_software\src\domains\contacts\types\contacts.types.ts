// Base Contact interface
export interface Contact {
  id: number;
  name: string;
  contact_type: 'customer' | 'vendor' | 'employee' | 'other';
  email?: string;
  phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
}

// Customer interface
export interface Customer {
  contact: number;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_type?: string;
  customer_code?: string;
  credit_limit?: number;
  payment_terms?: string;
  customer_category?: string;
  discount_percentage?: number;
  tax_exempt: boolean;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  mobile?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country?: string;
  shipping_same_as_billing: boolean;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
}

// Vendor interface
export interface Vendor {
  contact: number;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_type?: string;
  vendor_code?: string;
  credit_limit?: number;
  payment_terms?: string;
  vendor_category?: string;
  lead_time_days: number;
  minimum_order_amount: number;
  preferred_vendor: boolean;
  bank_details?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  mobile?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country?: string;
  shipping_same_as_billing: boolean;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
}

// Employee interface
export interface Employee {
  contact: number;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_type?: string;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  mobile?: string;
  employee_id?: string;
  position?: string;
  department?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

// Form Data interfaces for creating/updating
export interface ContactFormData {
  name: string;
  contact_type: 'customer' | 'vendor' | 'employee' | 'other';
  email?: string;
  phone?: string;
  address?: string;
}

export interface CustomerFormData {
  contact: number;
  customer_code?: string;
  credit_limit?: number;
  payment_terms?: string;
  customer_category?: string;
  discount_percentage?: number;
  tax_exempt: boolean;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  mobile?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country?: string;
  shipping_same_as_billing: boolean;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
}

export interface VendorFormData {
  contact: number;
  vendor_code?: string;
  credit_limit?: number;
  payment_terms?: string;
  vendor_category?: string;
  lead_time_days: number;
  minimum_order_amount: number;
  preferred_vendor: boolean;
  bank_details?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  mobile?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country?: string;
  shipping_same_as_billing: boolean;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
}

export interface EmployeeFormData {
  contact: number;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  mobile?: string;
  employee_id?: string;
  position?: string;
  department?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

// API Response interfaces
export interface ContactsApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Contact[];
}

export interface CustomersApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Customer[];
}

export interface VendorsApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Vendor[];
}

export interface EmployeesApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Employee[];
} 