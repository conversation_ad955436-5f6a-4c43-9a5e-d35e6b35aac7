// src/components/pricing/PriceListManager.jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, DatePicker, Select, message, Card, Tag } from 'antd';
import { pricingService } from '../../services/pricingService';
import moment from 'moment';

const { Option } = Select;
const { TextArea } = Input;

const PriceListManager = () => {
  const [priceLists, setPriceLists] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState(null);

  const currencyOptions = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
    { value: 'JPY', label: 'Japanese Yen (¥)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'AUD', label: 'Australian Dollar (A$)' },
    { value: 'INR', label: 'Indian Rupee (₹)' },
    { value: 'CNY', label: 'Chinese Yuan (¥)' },
    { value: 'NGN', label: 'Nigerian Naira (₦)' },
    { value: 'ZAR', label: 'South African Rand (R)' },
  ];

  useEffect(() => {
    fetchPriceLists();
  }, []);

  const fetchPriceLists = async () => {
    setLoading(true);
    try {
      const response = await pricingService.getPriceLists();
      setPriceLists(response.data);
    } catch (error) {
      message.error('Failed to fetch price lists');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    form.setFieldsValue({
      ...record,
      valid_from: moment(record.valid_from),
      valid_to: record.valid_to ? moment(record.valid_to) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      values.valid_from = values.valid_from.format('YYYY-MM-DD');
      values.valid_to = values.valid_to?.format('YYYY-MM-DD');
      
      if (editMode) {
        await pricingService.updatePriceList(currentId, values);
        message.success('Price list updated successfully');
      } else {
        await pricingService.createPriceList(values);
        message.success('Price list created successfully');
      }
      
      setModalVisible(false);
      fetchPriceLists();
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to save price list');
    }
  };

  const handleDelete = async (id) => {
    try {
      await pricingService.deletePriceList(id);
      message.success('Price list deleted successfully');
      fetchPriceLists();
    } catch (error) {
      message.error('Failed to delete price list');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency) => {
        const currencyObj = currencyOptions.find(c => c.value === currency);
        return currencyObj?.label || currency;
      },
    },
    {
      title: 'Valid From',
      dataIndex: 'valid_from',
      key: 'valid_from',
      render: (date) => moment(date).format('YYYY-MM-DD'),
      sorter: (a, b) => new Date(a.valid_from) - new Date(b.valid_from),
    },
    {
      title: 'Valid To',
      dataIndex: 'valid_to',
      key: 'valid_to',
      render: (date) => date ? moment(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value, record) => record.is_active === value,
    },
    {
      title: 'Default',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault) => (
        <Tag color={isDefault ? 'blue' : 'default'}>
          {isDefault ? 'Yes' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>Edit</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>Delete</Button>
        </div>
      ),
    },
  ];

  return (
    <Card title="Price Lists" extra={<Button type="primary" onClick={handleCreate}>Create Price List</Button>}>
      <Table 
        columns={columns}
        dataSource={priceLists}
        rowKey="id"
        loading={loading}
        bordered
      />
      
      <Modal
        title={editMode ? 'Edit Price List' : 'Create New Price List'}
        visible={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        okText={editMode ? 'Update' : 'Create'}
        cancelText="Cancel"
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={3} />
          </Form.Item>
          
          <Form.Item
            name="currency"
            label="Currency"
            rules={[{ required: true, message: 'Please select a currency' }]}
          >
            <Select>
              {currencyOptions.map(currency => (
                <Option key={currency.value} value={currency.value}>
                  {currency.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="valid_from"
            label="Valid From"
            rules={[{ required: true, message: 'Please select a date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="valid_to"
            label="Valid To (optional)"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="Active"
            valuePropName="checked"
            initialValue={true}
          >
            <Input type="checkbox" />
          </Form.Item>
          
          <Form.Item
            name="is_default"
            label="Default Price List"
            valuePropName="checked"
            initialValue={false}
          >
            <Input type="checkbox" />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default PriceListManager;