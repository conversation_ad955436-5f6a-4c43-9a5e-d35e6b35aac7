// src/services/pricingService.ts
import axios from 'axios';

const API_BASE = '/api/pricing';

export interface PriceList {
  id: number;
  name: string;
  description?: string;
  currency: string;
  valid_from: string;
  valid_to?: string;
  is_active: boolean;
  is_default: boolean;
}

export interface PriceListItem {
  id: number;
  price_list: number;
  product: number;
  unit_price: number;
  min_quantity: number;
  discount_percent: number;
  effective_date: string;
  expiry_date?: string;
}

export interface DiscountRule {
  id: number;
  name: string;
  description?: string;
  discount_percent: number;
  min_quantity?: number;
  customer_group?: string;
  product_category?: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
}

export interface PriceCalculationParams {
  product: number;
  customer?: number;
  quantity?: number;
  date?: string;
}

export interface PriceCalculationResult {
  product_id: number;
  product_code: string;
  product_name: string;
  customer_id?: number;
  quantity: number;
  unit_price: number;
  currency: string;
}

export const pricingService = {
  // Price Lists
  getPriceLists: () => axios.get<PriceList[]>(`${API_BASE}/price-lists/`),
  getPriceList: (id: number) => axios.get<PriceList>(`${API_BASE}/price-lists/${id}/`),
  createPriceList: (data: Partial<PriceList>) => axios.post<PriceList>(`${API_BASE}/price-lists/`, data),
  updatePriceList: (id: number, data: Partial<PriceList>) => axios.put<PriceList>(`${API_BASE}/price-lists/${id}/`, data),
  deletePriceList: (id: number) => axios.delete(`${API_BASE}/price-lists/${id}/`),
  
  // Price List Items
  getPriceListItems: (params?: any) => axios.get<PriceListItem[]>(`${API_BASE}/price-list-items/`, { params }),
  createPriceListItem: (data: Partial<PriceListItem>) => axios.post<PriceListItem>(`${API_BASE}/price-list-items/`, data),
  updatePriceListItem: (id: number, data: Partial<PriceListItem>) => axios.put<PriceListItem>(`${API_BASE}/price-list-items/${id}/`, data),
  deletePriceListItem: (id: number) => axios.delete(`${API_BASE}/price-list-items/${id}/`),
  
  // Discount Rules
  getDiscountRules: (params?: any) => axios.get<DiscountRule[]>(`${API_BASE}/discount-rules/`, { params }),
  createDiscountRule: (data: Partial<DiscountRule>) => axios.post<DiscountRule>(`${API_BASE}/discount-rules/`, data),
  updateDiscountRule: (id: number, data: Partial<DiscountRule>) => axios.put<DiscountRule>(`${API_BASE}/discount-rules/${id}/`, data),
  deleteDiscountRule: (id: number) => axios.delete(`${API_BASE}/discount-rules/${id}/`),
  
  // Pricing Calculations
  calculatePrice: (params: PriceCalculationParams) => axios.get<PriceCalculationResult>(`${API_BASE}/pricing/get_price/`, { params }),
  
  // Default Price List
  getDefaultPriceList: () => axios.get<PriceList>(`${API_BASE}/price-lists/default/`),
};
