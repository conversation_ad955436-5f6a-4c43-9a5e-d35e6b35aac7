// src/components/pricing/PriceListItemManager.tsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, InputNumber, DatePicker, Select, message, Card } from 'antd';
import { pricingService, PriceListItem, PriceList } from '../../../services/pricingService';
import dayjs from 'dayjs';

const { Option } = Select;

interface Product {
  id: number;
  code: string;
  name: string;
}

const PriceListItemManager: React.FC = () => {
  const [priceListItems, setPriceListItems] = useState<PriceListItem[]>([]);
  const [priceLists, setPriceLists] = useState<PriceList[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [itemsRes, priceListsRes, productsRes] = await Promise.all([
        pricingService.getPriceListItems(),
        pricingService.getPriceLists(),
        // You might need to adjust this endpoint based on your backend
        fetch('/api/pricing/products/').then(res => res.json()),
      ]);
      
      setPriceListItems(itemsRes.data);
      setPriceLists(priceListsRes.data);
      setProducts(productsRes);
    } catch (error) {
      message.error('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditMode(false);
    setCurrentId(null);
    setModalVisible(true);
  };

  const handleEdit = (record: PriceListItem) => {
    form.setFieldsValue({
      ...record,
      effective_date: record.effective_date ? dayjs(record.effective_date) : null,
      expiry_date: record.expiry_date ? dayjs(record.expiry_date) : null,
    });
    setEditMode(true);
    setCurrentId(record.id);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await pricingService.deletePriceListItem(id);
      message.success('Price list item deleted successfully');
      fetchData();
    } catch (error) {
      message.error('Failed to delete price list item');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const data = {
        ...values,
        effective_date: values.effective_date ? values.effective_date.format('YYYY-MM-DD') : null,
        expiry_date: values.expiry_date ? values.expiry_date.format('YYYY-MM-DD') : null,
      };

      if (editMode && currentId) {
        await pricingService.updatePriceListItem(currentId, data);
        message.success('Price list item updated successfully');
      } else {
        await pricingService.createPriceListItem(data);
        message.success('Price list item created successfully');
      }

      setModalVisible(false);
      fetchData();
    } catch (error) {
      message.error('Failed to save price list item');
    }
  };

  const columns = [
    {
      title: 'Price List',
      dataIndex: 'price_list',
      key: 'price_list',
      render: (priceListId: number) => {
        const priceList = priceLists.find(pl => pl.id === priceListId);
        return priceList ? priceList.name : 'Unknown';
      },
    },
    {
      title: 'Product',
      dataIndex: 'product',
      key: 'product',
      render: (productId: number) => {
        const product = products.find(p => p.id === productId);
        return product ? `${product.code} - ${product.name}` : 'Unknown';
      },
    },
    {
      title: 'Unit Price',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (price: number) => `$${price.toFixed(2)}`,
    },
    {
      title: 'Min Quantity',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
    },
    {
      title: 'Discount %',
      dataIndex: 'discount_percent',
      key: 'discount_percent',
      render: (percent: number) => `${percent}%`,
    },
    {
      title: 'Effective Date',
      dataIndex: 'effective_date',
      key: 'effective_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'Expiry Date',
      dataIndex: 'expiry_date',
      key: 'expiry_date',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : 'No Expiry',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: PriceListItem) => (
        <div>
          <Button 
            type="link" 
            onClick={() => handleEdit(record)}
            style={{ padding: 0, marginRight: 8 }}
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => handleDelete(record.id)}
            style={{ padding: 0 }}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Card title="Price List Items Management">
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleCreate}>
          Add Price List Item
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={priceListItems}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editMode ? 'Edit Price List Item' : 'Add Price List Item'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="price_list"
            label="Price List"
            rules={[{ required: true, message: 'Please select a price list' }]}
          >
            <Select placeholder="Select price list">
              {priceLists.map(priceList => (
                <Option key={priceList.id} value={priceList.id}>
                  {priceList.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="product"
            label="Product"
            rules={[{ required: true, message: 'Please select a product' }]}
          >
            <Select placeholder="Select product" showSearch optionFilterProp="children">
              {products.map(product => (
                <Option key={product.id} value={product.id}>
                  {product.code} - {product.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="unit_price"
            label="Unit Price"
            rules={[{ required: true, message: 'Please enter unit price' }]}
          >
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="Enter unit price"
            />
          </Form.Item>

          <Form.Item
            name="min_quantity"
            label="Minimum Quantity"
            rules={[{ required: true, message: 'Please enter minimum quantity' }]}
            initialValue={1}
          >
            <InputNumber
              min={0.01}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="Enter minimum quantity"
            />
          </Form.Item>

          <Form.Item
            name="discount_percent"
            label="Discount Percentage"
            initialValue={0}
          >
            <InputNumber
              min={0}
              max={100}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="Enter discount percentage"
            />
          </Form.Item>

          <Form.Item
            name="effective_date"
            label="Effective Date"
            rules={[{ required: true, message: 'Please select effective date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="expiry_date"
            label="Expiry Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default PriceListItemManager;
