import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  styled,
  Theme,
  Box,
  Typography,
} from '@mui/material';
import * as MuiIcons from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

// Constants
const DRAWER_WIDTH = 275;

// Types
interface MenuItem {
  id: number;
  text: string;
  icon: string;
  path?: string;
  sort_order?: number;
  subItems?: MenuItem[];
}

// Styled Components
const StyledDrawer = styled(Drawer)(({ theme }: { theme: Theme }) => ({
  width: DRAWER_WIDTH,
  flexShrink: 0,
  position: 'relative',
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.background.paper,
    borderRight: `1px solid ${theme.palette.divider}`,
    marginTop: 0,
    paddingRight: 0,
    position: 'relative',
    height: '100%',
    overflow: 'auto',
  },
}));

const Logo = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
}));

const LogoIcon = styled(MuiIcons.AccountBalance)(({ theme }) => ({
  fontSize: '2.5rem',
  color: theme.palette.primary.main,
}));

const LogoText = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: theme.palette.primary.main,
  letterSpacing: '0.5px',
  lineHeight: 1.2,
  textTransform: 'none',
  fontFamily: theme.typography.fontFamily,
}));

interface StyledListItemProps {
  isactive: 'true' | 'false';
}

const StyledListItem = styled(ListItem, {
  shouldForwardProp: (prop) => prop !== 'isactive'
})<StyledListItemProps>(({ theme, isactive }) => ({
  paddingLeft: theme.spacing(2),
  paddingTop: theme.spacing(0.75), // Reduced vertical padding
  paddingBottom: theme.spacing(0.75), // Reduced vertical padding
  cursor: 'pointer',
  backgroundColor: isactive === 'true' ? theme.palette.action.selected : 'transparent',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
  '& .MuiListItemIcon-root': {
    minWidth: 40,
    color: isactive === 'true' ? theme.palette.primary.main : theme.palette.text.secondary,
  },
  '& .MuiListItemText-primary': {
    color: isactive === 'true' ? theme.palette.primary.main : theme.palette.text.primary,
    fontWeight: isactive === 'true' ? 600 : 400,
    fontSize: (props: any) => props['data-is-dashboard'] ? '1.1rem' : 'inherit', // Slightly reduced dashboard text size
  },
  ...(isactive === 'true' && {
    borderRight: `3px solid ${theme.palette.primary.main}`,
  }),
}));

const StyledList = styled(List)(({ theme }) => ({
  padding: theme.spacing(1, 0), // Reduced from 2 to 1
  '& .MuiCollapse-root': {
    backgroundColor: theme.palette.grey[50],
  },
  // Add styles for nested lists
  '& .MuiList-root': {
    padding: 0, // Remove default padding from nested lists
  },
}));

// Default menu items matching the routes in App.tsx
const DEFAULT_MENU_ITEMS: MenuItem[] = [
  {
    id: 1,
    text: 'Dashboard',
    icon: 'Dashboard',
    path: '/dashboard',
    sort_order: 1,
  },
  {
    id: 2,
    text: 'Setup',
    icon: 'Settings',
    sort_order: 2,
    subItems: [
      {
        id: 3,
        text: 'Company',
        icon: 'Business',
        path: '/dashboard/setup/company',
        sort_order: 1,
        subItems: [
          {
            id: 1001,
            text: 'Sales Tax',
            icon: 'Percent',
            path: '/dashboard/setup/company/sales-tax',
            sort_order: 10,
          },
          {
            id: 1002,
            text: 'TDS',
            icon: 'AccountBalance',
            path: '/dashboard/setup/company/tds',
            sort_order: 11,
          },
        ],
      },
      {
        id: 4,
        text: 'Company Settings',
        icon: 'BusinessCenter',
        path: '/dashboard/setup/company/settings',
        sort_order: 2,
      },
      {
        id: 5,
        text: 'Users',
        icon: 'People',
        path: '/dashboard/setup/users',
        sort_order: 3,
      },
    ],
  },
  {
    id: 6,
    text: 'Sales',
    icon: 'ShoppingCart',
    sort_order: 3,
    subItems: [
      {
        id: 7,
        text: 'All Sales',
        icon: 'Assessment',
        path: '/dashboard/sales/all',
        sort_order: 1,
      },
      {
        id: 8,
        text: 'Invoices',
        icon: 'Receipt',
        path: '/dashboard/sales/invoices',
        sort_order: 2,
      },
      {
        id: 9,
        text: 'Estimates',
        icon: 'Description',
        path: '/dashboard/sales/estimates',
        sort_order: 3,
      },
      {
        id: 10,
        text: 'Customers',
        icon: 'Group',
        path: '/dashboard/sales/customers',
        sort_order: 4,
      },
      {
        id: 11,
        text: 'Products & Services',
        icon: 'Inventory',
        path: '/dashboard/sales/products',
        sort_order: 5,
      }
    ],
  },
  {
    id: 12,
    text: 'Purchases',
    icon: 'ShoppingBasket',
    sort_order: 4,
    subItems: [
      {
        id: 13,
        text: 'All Purchases',
        icon: 'Assessment',
        path: '/dashboard/purchases/all',
        sort_order: 1,
      },
      {
        id: 14,
        text: 'Bills',
        icon: 'Receipt',
        path: '/dashboard/purchases/bills',
        sort_order: 2,
      },
      {
        id: 15,
        text: 'Purchase Orders',
        icon: 'Description',
        path: '/dashboard/purchases/orders',
        sort_order: 3,
      },
      {
        id: 16,
        text: 'Vendors',
        icon: 'Business',
        path: '/dashboard/purchases/vendors',
        sort_order: 4,
      },
      {
        id: 17,
        text: 'Products & Services',
        icon: 'Inventory',
        path: '/dashboard/purchases/products',
        sort_order: 5,
      }
    ],
  },

  // Inventory Management Module
  {
    id: 1301,
    text: 'Inventory Management',
    icon: 'Warehouse',
    sort_order: 4.7,
    subItems: [
      {
        id: 1302,
        text: 'Inventory Dashboard',
        icon: 'Dashboard',
        path: '/dashboard/inventory',
        sort_order: 1,
      },
      {
        id: 1303,
        text: 'Goods Receipt Notes',
        icon: 'Receipt',
        path: '/dashboard/inventory/grn',
        sort_order: 2,
      },
      {
        id: 1307,
        text: 'Good Return Notes',
        icon: 'Undo',
        path: '/dashboard/inventory/grn-returns',
        sort_order: 3,
      },
      {
        id: 1304,
                        text: 'Stock Ledgers',
        icon: 'Inventory',
        path: '/dashboard/inventory/stock',
        sort_order: 4,
      },
      {
        id: 1305,
        text: 'Stock Transactions',
        icon: 'Timeline',
        path: '/dashboard/inventory/transactions',
        sort_order: 5,
      },
      {
        id: 1306,
        text: 'Warehouses',
        icon: 'Business',
        path: '/dashboard/inventory/warehouses',
        sort_order: 6,
      }
    ],
  },
  // Adding HR Module
  {
    id: 18,
    text: 'Human Resources',
    icon: 'Groups',
    sort_order: 5,
    subItems: [
      {
        id: 19,
        text: 'HR Dashboard',
        icon: 'Dashboard',
        path: '/dashboard/hr/dashboard',
        sort_order: 1,
      },
      {
        id: 20,
        text: 'Employees',
        icon: 'People',
        path: '/dashboard/hr/employees',
        sort_order: 2,
      },
      {
        id: 21,
        text: 'Departments',
        icon: 'Business',
        path: '/dashboard/hr/departments',
        sort_order: 3,
      },
      {
        id: 22,
        text: 'Recruitment',
        icon: 'Assignment',
        path: '/dashboard/hr/recruitment',
        sort_order: 4,
      },
      {
        id: 23,
        text: 'Time Off',
        icon: 'EventBusy',
        path: '/dashboard/hr/time-off',
        sort_order: 5,
      },
      {
        id: 24,
        text: 'Attendance',
        icon: 'EventNote',
        path: '/dashboard/hr/attendance',
        sort_order: 6,
      },
    ],
  },
  // Adding General Ledger Module
  {
    id: 25,
    text: 'General Ledger',
    icon: 'AccountBalance',
    sort_order: 6,
    subItems: [
      {
        id: 26,
        text: 'Chart of Accounts',
        icon: 'AccountTree',
        path: '/dashboard/gl/chart-of-accounts',
        sort_order: 1,
      },
      {
        id: 27,
        text: 'Journal Entries',
        icon: 'Receipt',
        path: '/dashboard/gl/journal-entries',
        sort_order: 2,
      },
      {
        id: 28,
        text: 'Trial Balance',
        icon: 'Balance',
        path: '/dashboard/gl/trial-balance',
        sort_order: 3,
      },
      {
        id: 29,
        text: 'Account Ledgers',
        icon: 'MenuBook',
        path: '/dashboard/gl/account-ledgers',
        sort_order: 4,
      },
      {
        id: 30,
        text: 'Financial Reports',
        icon: 'Assessment',
        path: '/dashboard/gl/reports',
        sort_order: 5,
      },
      {
        id: 46,
        text: 'Purchase Orders Review',
        icon: 'Receipt',
        path: '/dashboard/gl/purchase-orders',
        sort_order: 6,
      },
      {
        id: 47,
        text: 'GRN Receipts Review',
        icon: 'Inventory',
        path: '/dashboard/gl/grn-receipts',
        sort_order: 7,
      },
      {
        id: 48,
        text: 'GRN Returns Review',
        icon: 'AssignmentReturn',
        path: '/dashboard/gl/grn-returns',
        sort_order: 8,
      },
      {
        id: 49,
        text: 'Stock Transactions Review',
        icon: 'SwapHoriz',
        path: '/dashboard/gl/stock-transactions',
        sort_order: 9,
      },
    ],
  },
  // Adding Cash Management System
  {
    id: 31,
    text: 'Cash Management',
    icon: 'AccountBalanceWallet',
    sort_order: 7,
    subItems: [
      {
        id: 32,
        text: 'Treasury Dashboard',
        icon: 'Dashboard',
        path: '/dashboard/cms/dashboard',
        sort_order: 1,
      },
      {
        id: 33,
        text: 'Cash Accounts',
        icon: 'AccountBalance',
        path: '/dashboard/cms/accounts',
        sort_order: 2,
      },
      {
        id: 34,
        text: 'Transactions',
        icon: 'Receipt',
        path: '/dashboard/cms/transactions',
        sort_order: 3,
      },
      {
        id: 35,
        text: 'Transfers',
        icon: 'SwapHoriz',
        path: '/dashboard/cms/transfers',
        sort_order: 4,
      },
      {
        id: 36,
        text: 'Bank Reconciliation',
        icon: 'AccountBalance',
        path: '/dashboard/cms/reconciliation',
        sort_order: 5,
      },
      {
        id: 37,
        text: 'Cash Payment',
        icon: 'Payment',
        path: '/dashboard/cms/payments/new',
        sort_order: 6,
      },
      {
        id: 38,
        text: 'Cash Receipt',
        icon: 'Receipt',
        path: '/dashboard/cms/receipts/new',
        sort_order: 7,
      },
    ],
  },
  // Adding Financial Planning & Analysis Module
  {
    id: 39,
    text: 'Financial Planning & Analysis',
    icon: 'TrendingUp',
    sort_order: 8,
    subItems: [
      {
        id: 40,
        text: 'FP&A Dashboard',
        icon: 'Assessment',
        path: '/dashboard/fpa/dashboard',
        sort_order: 1,
      },
      {
        id: 41,
        text: 'Budget Planning',
        icon: 'Timeline',
        path: '/dashboard/fpa/budget-planning',
        sort_order: 2,
      },
      {
        id: 42,
        text: 'Variance Analysis',
        icon: 'BarChart',
        path: '/dashboard/fpa/variance-analysis',
        sort_order: 3,
      },
      {
        id: 43,
        text: 'Financial Models',
        icon: 'ModelTraining',
        path: '/dashboard/fpa/financial-models',
        sort_order: 4,
      },
      {
        id: 44,
        text: 'Forecasting',
        icon: 'ShowChart',
        path: '/dashboard/fpa/forecasting',
        sort_order: 5,
      },
      {
        id: 45,
        text: 'Plan vs Actual',
        icon: 'Compare',
        path: '/dashboard/fpa/plan-vs-actual',
        sort_order: 6,
      },
    ],
  },
];

const Sidebar: React.FC = () => {
  const [openMenus, setOpenMenus] = useState<Set<string>>(new Set());
  const [menuItems] = useState<MenuItem[]>(DEFAULT_MENU_ITEMS);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    expandMenuContainingPath(menuItems, location.pathname);
  }, [location.pathname]);

  const expandMenuContainingPath = (items: MenuItem[], targetPath: string): void => {
    // Special case for Products & Services pages
    if (targetPath === '/dashboard/sales/products') {
      setOpenMenus(prev => new Set([...prev, 'Sales']));
    } else if (targetPath === '/dashboard/purchases/products') {
      setOpenMenus(prev => new Set([...prev, 'Purchases']));
    } else if (targetPath.startsWith('/dashboard/hr/')) {
      setOpenMenus(prev => new Set([...prev, 'Human Resources']));
    } else if (targetPath.startsWith('/dashboard/gl/')) {
      setOpenMenus(prev => new Set([...prev, 'General Ledger']));
    } else if (targetPath.startsWith('/dashboard/cms/')) {
      setOpenMenus(prev => new Set([...prev, 'Cash Management']));
    } else if (targetPath.startsWith('/dashboard/fpa/')) {
      setOpenMenus(prev => new Set([...prev, 'Financial Planning & Analysis']));
    } else if (targetPath.startsWith('/dashboard/inventory/')) {
      setOpenMenus(prev => new Set([...prev, 'Inventory Management']));
    }

    const findAndExpandPath = (menuItems: MenuItem[]): boolean => {
      for (const item of menuItems) {
        // Check if the current path exactly matches the item's path
        if (item.path === targetPath) {
          return true;
        }
        
        // Check if the current path starts with the item's path (for parent routes)
        if (item.path && targetPath.startsWith(item.path) && item.subItems?.length) {
          setOpenMenus(prev => new Set([...prev, item.text]));
          return true;
        }
        
        // Check in subitems
        if (item.subItems?.length) {
          const foundInChildren = findAndExpandPath(item.subItems);
          if (foundInChildren) {
            setOpenMenus(prev => new Set([...prev, item.text]));
            return true;
          }
        }
      }
      return false;
    };

    findAndExpandPath(items);
  };

  const handleMenuClick = (item: MenuItem): void => {
    console.log('Sidebar: Menu item clicked:', item.text, 'Path:', item.path);
    
    // Special case for Products & Services
    if (item.text === 'Products & Services') {
      if (item.path?.includes('purchases')) {
        navigate('/dashboard/purchases/products');
      } else {
        navigate('/dashboard/sales/products');
      }
      return;
    }

    if (item.subItems?.length) {
      console.log('Sidebar: Toggling submenu for:', item.text);
      setOpenMenus(prev => {
        const newSet = new Set(prev);
        if (newSet.has(item.text)) {
          newSet.delete(item.text);
        } else {
          newSet.add(item.text);
        }
        return newSet;
      });
    } else if (item.path) {
      console.log('Sidebar: Navigating to:', item.path);
      navigate(item.path);
    }
  };

  const getIcon = (iconName: string): React.ReactElement => {
    const Icon = (MuiIcons as any)[iconName];
    return Icon ? <Icon /> : <MuiIcons.Circle />;
  };

  const renderMenuItem = (item: MenuItem, depth = 0): React.ReactNode => {
    const isOpen = openMenus.has(item.text);
    const isActive = item.path ? location.pathname === item.path : false;
    const isActiveParent = item.subItems?.some(subItem => 
      subItem.path ? location.pathname === subItem.path : false
    ) || (item.text === 'Human Resources' && location.pathname.startsWith('/dashboard/hr/'))
      || (item.text === 'General Ledger' && location.pathname.startsWith('/dashboard/gl/'))
      || (item.text === 'Financial Planning & Analysis' && location.pathname.startsWith('/dashboard/fpa/'));
    
    const isDashboard = item.text === 'Dashboard';

    // Special case for Products & Services
    const isProductsPage = item.text === 'Products & Services' && (
      (location.pathname === '/dashboard/sales/products' && !item.path?.includes('purchases')) ||
      (location.pathname === '/dashboard/purchases/products' && item.path?.includes('purchases'))
    );

    return (
      <React.Fragment key={item.id}>
        <StyledListItem
          onClick={() => handleMenuClick(item)}
          isactive={(isActive || isActiveParent || isProductsPage) ? 'true' : 'false'}
          sx={{ pl: depth * 2 + 2 }}
          data-is-dashboard={isDashboard}
        >
          <ListItemIcon>{getIcon(item.icon)}</ListItemIcon>
          <ListItemText 
            primary={item.text}
            primaryTypographyProps={{
              fontSize: isDashboard ? '1.25rem' : 'inherit',
              fontWeight: isDashboard ? 600 : ((isActive || isActiveParent || isProductsPage) ? 600 : 400)
            }}
          />
          {item.subItems && item.subItems.length > 0 && (
            isOpen ? <MuiIcons.ExpandLess /> : <MuiIcons.ExpandMore />
          )}
        </StyledListItem>
        {item.subItems && (
          <Collapse in={isOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.subItems.map(subItem => renderMenuItem(subItem, depth + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <StyledDrawer variant="permanent">
      <StyledList>
        {menuItems.map(item => renderMenuItem(item))}
      </StyledList>
    </StyledDrawer>
  );
};

export default Sidebar;
