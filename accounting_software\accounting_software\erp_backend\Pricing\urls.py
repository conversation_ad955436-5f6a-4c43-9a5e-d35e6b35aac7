from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from . import views

router = DefaultRouter()
router.register(r'price-lists', views.PriceListViewSet)
router.register(r'price-list-items', views.PriceListItemViewSet)
router.register(r'discount-rules', views.DiscountRuleViewSet)
router.register(r'pricing', views.PricingViewSet, basename='pricing')

urlpatterns = [
    path('', include(router.urls)),
]