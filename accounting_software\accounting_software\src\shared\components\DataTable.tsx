import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Box,
  styled,
  TableSortLabel,
  TextField,
  InputAdornment,
  Typography,
  Checkbox,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  Tooltip,
  Button,
  Divider,
  CircularProgress,
  useTheme,
  alpha,
  Collapse,
  Grid,
  Toolbar,
  Autocomplete,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  ViewColumn as ViewColumnIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { formatCurrency, formatNumber } from '../utils/formatters';

// Styled Components
const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
  border: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  overflow: 'hidden',
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.primary.main, 0.04),
  '& th': {
    fontWeight: 600,
    color: theme.palette.text.primary,
    borderBottom: `1px solid ${theme.palette.divider}`,
    padding: theme.spacing(1.5, 2),
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  padding: theme.spacing(1.5, 2),
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTableRow = styled(TableRow)<{ isselected?: string }>(({ theme, isselected }) => ({
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
  ...(isselected === 'true' && {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.12),
    },
  }),
}));

const SearchBox = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    '& fieldset': {
      borderColor: theme.palette.divider,
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
}));

const TableToolbar = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const ExpandableRow = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: alpha(theme.palette.background.default, 0.5),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StatusChip = styled(Chip)<{ statuscolor?: string }>(({ theme, statuscolor }) => ({
  borderRadius: '4px',
  fontWeight: 500,
  fontSize: '0.75rem',
  ...(statuscolor === 'success' && {
    backgroundColor: alpha(theme.palette.success.main, 0.1),
    color: theme.palette.success.dark,
    border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
  }),
  ...(statuscolor === 'warning' && {
    backgroundColor: alpha(theme.palette.warning.main, 0.1),
    color: theme.palette.warning.dark,
    border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
  }),
  ...(statuscolor === 'error' && {
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.dark,
    border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
  }),
  ...(statuscolor === 'info' && {
    backgroundColor: alpha(theme.palette.info.main, 0.1),
    color: theme.palette.info.dark,
    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
  }),
  ...(statuscolor === 'default' && {
    backgroundColor: alpha(theme.palette.grey[500], 0.1),
    color: theme.palette.grey[700],
    border: `1px solid ${alpha(theme.palette.grey[500], 0.2)}`,
  }),
}));

// Types
export interface Column {
  field: string;
  headerName: string;
  width?: number;
  minWidth?: number;
  flex?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  valueGetter?: (params: { row: any }) => any;
  renderCell?: (params: { row: any; value: any }) => React.ReactNode;
  disableClickEventBubbling?: boolean;
  format?: (value: any) => string;
  type?: 'string' | 'number' | 'date' | 'currency' | 'boolean';
  currencyCode?: string | ((params: { row: any }) => string);
  searchable?: boolean;
  options?: any[];
  optionLabel?: string;
  optionValue?: string;
  hide?: boolean;
}

export interface RowAction {
  icon: React.ReactNode;
  label: string;
  onClick: (row: any) => void;
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  tooltip?: string;
  hide?: (row: any) => boolean;
  disabled?: (row: any) => boolean;
}

export interface DataTableProps {
  columns: Column[];
  rows: any[];
  title?: string;
  loading?: boolean;
  pageSize?: number;
  pageSizeOptions?: number[];
  checkboxSelection?: boolean;
  onRowClick?: (row: any) => void;
  onSelectionChange?: (selectedRows: any[]) => void;
  rowActions?: RowAction[];
  toolbarActions?: React.ReactNode;
  expandableRows?: boolean;
  renderExpandedRow?: (row: any) => React.ReactNode;
  getRowId?: (row: any) => string | number;
  noDataText?: string;
  emptyIcon?: React.ReactNode;
  onRefresh?: () => void;
  stickyHeader?: boolean;
  dense?: boolean;
  showToolbar?: boolean;
  initialState?: {
    sorting?: { field: string; sort: 'asc' | 'desc' };
    pagination?: { page: number; pageSize: number };
    hiddenColumns?: string[];
  };
  onCellChange?: (row: any, field: string, value: any) => void;
  // Server-side pagination props
  serverSide?: boolean;
  totalCount?: number;
  page?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  rows,
  title,
  loading = false,
  pageSize = 10,
  pageSizeOptions = [5, 10, 25, 50, 100],
  checkboxSelection = false,
  onRowClick,
  onSelectionChange,
  rowActions,
  toolbarActions,
  expandableRows = false,
  renderExpandedRow,
  getRowId = (row) => row.id,
  noDataText = 'No records found',
  emptyIcon,
  onRefresh,
  stickyHeader = false,
  dense = false,
  showToolbar = true,
  initialState,
  onCellChange,
  // Server-side pagination props
  serverSide = false,
  totalCount,
  page: externalPage,
  onPageChange,
  onPageSizeChange,
}) => {
  const theme = useTheme();
  const [page, setPage] = useState(serverSide ? (externalPage || 0) : (initialState?.pagination?.page || 0));
  const [rowsPerPage, setRowsPerPage] = useState(initialState?.pagination?.pageSize || pageSize);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderBy, setOrderBy] = useState(initialState?.sorting?.field || '');
  const [order, setOrder] = useState<'asc' | 'desc'>(initialState?.sorting?.sort || 'asc');
  const [selected, setSelected] = useState<(string | number)[]>([]);
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({});
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [currentActionRow, setCurrentActionRow] = useState<any>(null);
  const [columnVisibility, setColumnVisibility] = useState<{ [key: string]: boolean }>(
    initialState?.hiddenColumns
      ? columns.reduce(
          (acc, column) => ({
            ...acc,
            [column.field]: !initialState.hiddenColumns?.includes(column.field),
          }),
          {}
        )
      : columns.reduce((acc, column) => ({ ...acc, [column.field]: !column.hide }), {})
  );
  const [columnMenuAnchorEl, setColumnMenuAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    if (onSelectionChange) {
      const selectedRows = (rows || []).filter((row) => selected.includes(getRowId(row)));
      onSelectionChange(selectedRows);
    }
  }, [selected, rows, onSelectionChange, getRowId]);

  const handleChangePage = (event: unknown, newPage: number) => {
    if (serverSide && onPageChange) {
      onPageChange(newPage);
    } else {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = parseInt(event.target.value, 10);
    setRowsPerPage(newPageSize);
    
    if (serverSide) {
      if (onPageSizeChange) {
        onPageSizeChange(newPageSize);
      }
      if (onPageChange) {
        onPageChange(0); // Reset to first page when changing page size
      }
    } else {
      setPage(0);
    }
  };

  const handleSort = (field: string) => {
    const isAsc = orderBy === field && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(field);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = (rows || []).map((row) => getRowId(row));
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleRowSelect = (id: string | number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: (string | number)[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter((item) => item !== id);
    }

    setSelected(newSelected);
  };

  const handleRowClick = (row: any) => {
    if (onRowClick) {
      onRowClick(row);
    }
  };

  const handleRowActionClick = (
    event: React.MouseEvent<HTMLButtonElement>,
    row: any
  ) => {
    event.stopPropagation();
    setCurrentActionRow(row);
    setAnchorEl(event.currentTarget);
  };

  const handleRowActionClose = () => {
    setAnchorEl(null);
    setCurrentActionRow(null);
  };

  const handleRowActionItemClick = (action: RowAction, row: any) => {
    action.onClick(row);
    handleRowActionClose();
  };

  const handleExpandRow = (event: React.MouseEvent<HTMLButtonElement>, id: string | number) => {
    event.stopPropagation();
    setExpandedRows((prev) => ({
      ...prev,
      [id.toString()]: !prev[id.toString()],
    }));
  };

  const handleColumnVisibilityChange = (field: string) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleColumnMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setColumnMenuAnchorEl(event.currentTarget);
  };

  const handleColumnMenuClose = () => {
    setColumnMenuAnchorEl(null);
  };

  const isSelected = (id: string | number) => selected.indexOf(id) !== -1;

  // Filter and sort rows
  const filteredRows = (rows || []).filter((row) => {
    if (!searchTerm) return true;
    
    return columns.some((column) => {
      if (!columnVisibility[column.field]) return false;
      
      let value;
      if (column.valueGetter) {
        value = column.valueGetter({ row });
      } else {
        value = row[column.field];
      }
      
      if (value == null) return false;
      
      return String(value).toLowerCase().includes(searchTerm.toLowerCase());
    });
  });

  const sortedRows = orderBy
    ? [...filteredRows].sort((a, b) => {
        let aValue, bValue;
        
        const column = columns.find((col) => col.field === orderBy);
        
        if (column?.valueGetter) {
          aValue = column.valueGetter({ row: a });
          bValue = column.valueGetter({ row: b });
        } else {
          aValue = a[orderBy];
          bValue = b[orderBy];
        }
        
        // Handle null or undefined values
        if (aValue == null) return order === 'asc' ? -1 : 1;
        if (bValue == null) return order === 'asc' ? 1 : -1;
        
        // Compare based on type
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return order === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        return order === 'asc'
          ? aValue < bValue ? -1 : aValue > bValue ? 1 : 0
          : bValue < aValue ? -1 : bValue > aValue ? 1 : 0;
      })
    : filteredRows;

  const paginatedRows = serverSide 
    ? sortedRows // For server-side pagination, use all rows (already paginated by server)
    : sortedRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const visibleColumns = columns.filter((column) => columnVisibility[column.field]);

  return (
    <Paper
      elevation={0}
      sx={{
        width: '100%',
        overflow: 'hidden',
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: theme.shape.borderRadius,
      }}
    >
      {showToolbar && (
        <TableToolbar>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {title && (
              <Typography
                variant="h6"
                sx={{ mr: 2, fontWeight: 600, color: theme.palette.text.primary }}
              >
                {title}
              </Typography>
            )}
            <SearchBox
              size="small"
              variant="outlined"
              placeholder="Search..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" fontSize="small" />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 250, mr: 2 }}
            />
            <Tooltip title="View columns">
              <IconButton size="small" onClick={handleColumnMenuOpen}>
                <ViewColumnIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={columnMenuAnchorEl}
              open={Boolean(columnMenuAnchorEl)}
              onClose={handleColumnMenuClose}
            >
              {columns.map((column) => (
                <MenuItem
                  key={column.field}
                  onClick={() => handleColumnVisibilityChange(column.field)}
                  dense
                >
                  <Checkbox
                    checked={columnVisibility[column.field]}
                    size="small"
                    color="primary"
                  />
                  <Typography variant="body2">{column.headerName}</Typography>
                </MenuItem>
              ))}
            </Menu>
            {onRefresh && (
              <Tooltip title="Refresh">
                <IconButton size="small" onClick={onRefresh}>
                  <RefreshIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {toolbarActions}
            <Tooltip title="Export as CSV">
              <IconButton size="small">
                <DownloadIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Print">
              <IconButton size="small">
                <PrintIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </TableToolbar>
      )}

      <StyledTableContainer sx={{ maxHeight: stickyHeader ? 440 : undefined }}>
        <Table stickyHeader={stickyHeader} size={dense ? 'small' : 'medium'}>
          <StyledTableHead>
            <TableRow>
              {checkboxSelection && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < (rows || []).length}
                    checked={(rows || []).length > 0 && selected.length === (rows || []).length}
                    onChange={handleSelectAllClick}
                    size="small"
                  />
                </TableCell>
              )}
              {expandableRows && <TableCell style={{ width: 50 }} />}
              {visibleColumns.map((column) => (
                <TableCell
                  key={column.field}
                  align={column.align || 'left'}
                  style={{
                    width: column.width,
                    minWidth: column.minWidth,
                    flex: column.flex,
                  }}
                  sortDirection={orderBy === column.field ? order : false}
                >
                  {column.sortable !== false ? (
                    <TableSortLabel
                      active={orderBy === column.field}
                      direction={orderBy === column.field ? order : 'asc'}
                      onClick={() => handleSort(column.field)}
                    >
                      {column.headerName}
                    </TableSortLabel>
                  ) : (
                    column.headerName
                  )}
                </TableCell>
              ))}
              {rowActions && <TableCell align="right" style={{ width: 60 }} />}
            </TableRow>
          </StyledTableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={
                    visibleColumns.length +
                    (checkboxSelection ? 1 : 0) +
                    (expandableRows ? 1 : 0) +
                    (rowActions ? 1 : 0)
                  }
                  align="center"
                  sx={{ py: 5 }}
                >
                  <CircularProgress size={40} />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    Loading...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : paginatedRows.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={
                    visibleColumns.length +
                    (checkboxSelection ? 1 : 0) +
                    (expandableRows ? 1 : 0) +
                    (rowActions ? 1 : 0)
                  }
                  align="center"
                  sx={{ py: 5 }}
                >
                  {emptyIcon}
                  <Typography variant="body1" sx={{ mt: 1 }}>
                    {noDataText}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              paginatedRows.map((row) => {
                const id = getRowId(row);
                const isItemSelected = isSelected(id);
                const isRowExpanded = expandableRows && expandedRows[id.toString()];

                return (
                  <React.Fragment key={id}>
                    <StyledTableRow
                      hover
                      onClick={() => handleRowClick(row)}
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      isselected={isItemSelected ? 'true' : 'false'}
                      sx={{ cursor: onRowClick ? 'pointer' : 'default' }}
                    >
                      {checkboxSelection && (
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={isItemSelected}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRowSelect(id);
                            }}
                            size="small"
                          />
                        </TableCell>
                      )}
                      {expandableRows && (
                        <TableCell>
                          <IconButton
                            aria-label="expand row"
                            size="small"
                            onClick={(event) => handleExpandRow(event, id)}
                          >
                            {isRowExpanded ? (
                              <KeyboardArrowUpIcon />
                            ) : (
                              <KeyboardArrowDownIcon />
                            )}
                          </IconButton>
                        </TableCell>
                      )}
                      {visibleColumns.map((column) => {
                        let cellContent;

                        if (column.renderCell) {
                          const value = column.valueGetter ? column.valueGetter({ row }) : row[column.field];
                          cellContent = column.renderCell({
                            row,
                            value
                          });
                        } else {
                          const value = column.valueGetter ? column.valueGetter({ row }) : row[column.field];
                          
                          if (column.format) {
                            cellContent = column.format(value);
                          } else if (column.type === 'currency' && typeof value === 'number') {
                            let currencyCode = 'USD';
                            if (typeof column.currencyCode === 'function') {
                              currencyCode = column.currencyCode({ row });
                            } else if (typeof column.currencyCode === 'string') {
                              currencyCode = column.currencyCode;
                            }
                            cellContent = formatCurrency(value, currencyCode);
                          } else if (column.type === 'number' && typeof value === 'number') {
                            cellContent = formatNumber(value, 2);
                          } else {
                            cellContent = value;
                          }
                        }

                        // Handle status chips
                        if (
                          column.field === 'status' &&
                          typeof cellContent === 'string' &&
                          !column.renderCell
                        ) {
                          let statusColor: 'success' | 'warning' | 'error' | 'info' | 'default' = 'default';
                          
                          if (['active', 'approved', 'completed', 'paid'].includes(cellContent.toLowerCase())) {
                            statusColor = 'success';
                          } else if (['pending', 'in progress', 'partial'].includes(cellContent.toLowerCase())) {
                            statusColor = 'warning';
                          } else if (['inactive', 'rejected', 'failed', 'overdue'].includes(cellContent.toLowerCase())) {
                            statusColor = 'error';
                          } else if (['new', 'info'].includes(cellContent.toLowerCase())) {
                            statusColor = 'info';
                          }
                          
                          cellContent = (
                            <StatusChip
                              label={cellContent}
                              size="small"
                              statuscolor={statusColor}
                            />
                          );
                        }

                        return (
                          <StyledTableCell
                            key={column.field}
                            align={column.align || 'left'}
                            onClick={(e) => {
                              if (column.disableClickEventBubbling) {
                                e.stopPropagation();
                              }
                            }}
                          >
                            {cellContent}
                          </StyledTableCell>
                        );
                      })}
                      {rowActions && (
                        <StyledTableCell align="right">
                          <IconButton
                            size="small"
                            onClick={(event) => handleRowActionClick(event, row)}
                          >
                            <MoreVertIcon fontSize="small" />
                          </IconButton>
                        </StyledTableCell>
                      )}
                    </StyledTableRow>
                    {expandableRows && isRowExpanded && (
                      <TableRow>
                        <TableCell
                          colSpan={
                            visibleColumns.length +
                            (checkboxSelection ? 1 : 0) +
                            (expandableRows ? 1 : 0) +
                            (rowActions ? 1 : 0)
                          }
                          sx={{ p: 0, borderBottom: 0 }}
                        >
                          <Collapse in={isRowExpanded} timeout="auto" unmountOnExit>
                            <ExpandableRow>
                              {renderExpandedRow ? renderExpandedRow(row) : null}
                            </ExpandableRow>
                          </Collapse>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })
            )}
          </TableBody>
        </Table>
      </StyledTableContainer>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          px: 2,
          py: 1,
          borderTop: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {checkboxSelection && selected.length > 0 && (
            <>
              <Typography variant="body2" sx={{ mr: 2 }}>
                {selected.length} selected
              </Typography>
              <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
            </>
          )}
          <Typography variant="body2" color="text.secondary">
            {serverSide ? (totalCount || 0) : filteredRows.length} records
          </Typography>
        </Box>
        <TablePagination
          rowsPerPageOptions={pageSizeOptions}
          component="div"
          count={serverSide ? (totalCount || 0) : filteredRows.length}
          rowsPerPage={rowsPerPage}
          page={serverSide ? (externalPage || 0) : page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Box>

      {/* Row Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleRowActionClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {rowActions?.map((action, index) => {
          if (currentActionRow && action.hide && action.hide(currentActionRow)) {
            return null;
          }
          
          const isDisabled = currentActionRow && action.disabled && action.disabled(currentActionRow);
          
          return (
            <MenuItem
              key={index}
              onClick={() => !isDisabled && currentActionRow && handleRowActionItemClick(action, currentActionRow)}
              disabled={isDisabled}
              sx={{ minWidth: 150 }}
            >
              <Box component="span" sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                {action.icon}
              </Box>
              <Typography variant="body2">{action.label}</Typography>
            </MenuItem>
          );
        })}
      </Menu>
    </Paper>
  );
};

// Common Row Actions
export const commonRowActions = {
  edit: (onClick: (row: any) => void): RowAction => ({
    icon: <EditIcon fontSize="small" color="primary" />,
    label: 'Edit',
    onClick,
    tooltip: 'Edit record',
  }),
  delete: (onClick: (row: any) => void): RowAction => ({
    icon: <DeleteIcon fontSize="small" color="error" />,
    label: 'Delete',
    onClick,
    tooltip: 'Delete record',
    color: 'error',
  }),
  view: (onClick: (row: any) => void): RowAction => ({
    icon: <EditIcon fontSize="small" color="info" />,
    label: 'View Details',
    onClick,
    tooltip: 'View details',
  }),
};

export default DataTable;