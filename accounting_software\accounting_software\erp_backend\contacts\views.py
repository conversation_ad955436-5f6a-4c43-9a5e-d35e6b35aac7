from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from .models import (
    Contact, Customer, Vendor, Employee
)
from .serializers import (
    ContactSerializer, CustomerSerializer, VendorSerializer, EmployeeSerializer
)


class ContactViewSet(viewsets.ModelViewSet):
    """ViewSet for managing all contacts"""
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['contact_type']
    search_fields = ['name', 'email', 'phone']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class CustomerViewSet(viewsets.ModelViewSet):
    """ViewSet for managing customers"""
    queryset = Customer.objects.select_related('contact').all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer_category', 'payment_terms', 'tax_exempt']
    search_fields = ['contact__name', 'contact__email', 'customer_code', 'company_name']
    ordering_fields = ['contact__name', 'customer_code', 'contact__created_at']
    ordering = ['contact__name']

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer statistics"""
        total_customers = self.get_queryset().count()
        active_customers = self.get_queryset().filter(contact__is_active=True).count()
        inactive_customers = total_customers - active_customers

        return Response({
            'total_customers': total_customers,
            'active_customers': active_customers,
            'inactive_customers': inactive_customers,
        })


class VendorViewSet(viewsets.ModelViewSet):
    """ViewSet for managing vendors"""
    queryset = Vendor.objects.select_related('contact').all()
    serializer_class = VendorSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['vendor_category', 'payment_terms', 'preferred_vendor']
    search_fields = ['contact__name', 'contact__email', 'vendor_code', 'company_name']
    ordering_fields = ['contact__name', 'vendor_code', 'contact__created_at']
    ordering = ['contact__name']


class EmployeeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing employees"""
    queryset = Employee.objects.select_related('contact').all()
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'position']
    search_fields = ['contact__name', 'contact__email', 'employee_id', 'position', 'department']
    ordering_fields = ['contact__name', 'employee_id', 'position', 'department']
    ordering = ['contact__name'] 