from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

class Command(BaseCommand):
    help = 'Create test user and token for authentication'

    def handle(self, *args, **options):
        # Create or get test user
        username = 'testuser'
        password = 'testpass123'
        email = '<EMAIL>'
        
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        if created:
            user.set_password(password)
            user.save()
            self.stdout.write(f'Created new user: {username}')
        else:
            self.stdout.write(f'User already exists: {username}')
        
        # Create or get token
        token, created = Token.objects.get_or_create(user=user)
        
        if created:
            self.stdout.write(f'Created new token: {token.key}')
        else:
            self.stdout.write(f'Token already exists: {token.key}')
        
        self.stdout.write(self.style.SUCCESS(f'''
Test credentials:
Username: {username}
Password: {password}
Email: {email}
Token: {token.key}

You can now use these credentials to login or use the token directly for API calls.
        ''')) 