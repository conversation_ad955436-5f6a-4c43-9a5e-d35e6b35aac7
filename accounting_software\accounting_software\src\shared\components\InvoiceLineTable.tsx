import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Autocomplete,
  Typography,
  Box,
  Button,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import FormattedCurrencyInput from './FormattedCurrencyInput';
import QuantityInput from './QuantityInput';

export interface InvoiceLineItem {
  id: string;
  product?: number;
  product_name?: string;
  description: string;
  quantity: number;
  unit_price: number;
  discount_percent?: number;
  line_total: number;
  taxable?: boolean;
  tax_rate?: number;
  tax_amount?: number;
}

export interface ProductOption {
  id: number;
  name: string;
  sku?: string;
  description?: string;
  unit_price?: number;
  sales_price?: number;
  cost_price?: number;
}

export interface InvoiceLineTableProps {
  lines: InvoiceLineItem[];
  products: ProductOption[];
  onLineChange: (lineId: string, field: string, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (lineId: string) => void;
  currencySymbol?: string;
  defaultTaxRate?: number;
  readOnly?: boolean;
  showTaxColumn?: boolean;
  showDiscountColumn?: boolean;
}

const InvoiceLineTable: React.FC<InvoiceLineTableProps> = ({
  lines,
  products,
  onLineChange,
  onAddLine,
  onRemoveLine,
  currencySymbol = '$',
  defaultTaxRate = 0,
  readOnly = false,
  showTaxColumn = true,
  showDiscountColumn = false,
}) => {
  
  const handleProductSelect = (lineId: string, product: ProductOption | null) => {
    if (product) {
      onLineChange(lineId, 'product', product.id);
      onLineChange(lineId, 'product_name', product.name);
      onLineChange(lineId, 'description', product.description || product.name);
      onLineChange(lineId, 'unit_price', product.sales_price || product.unit_price || 0);
      
      // Recalculate line total
      const line = lines.find(l => l.id === lineId);
      if (line) {
        const lineTotal = line.quantity * (product.sales_price || product.unit_price || 0);
        onLineChange(lineId, 'line_total', lineTotal);
        
        // Calculate tax if applicable
        if (showTaxColumn && line.tax_rate) {
          const taxAmount = (lineTotal * line.tax_rate) / 100;
          onLineChange(lineId, 'tax_amount', taxAmount);
        }
      }
    }
  };

  const handleQuantityChange = (lineId: string, quantity: number) => {
    onLineChange(lineId, 'quantity', quantity);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      let lineTotal = quantity * line.unit_price;
      
      // Apply discount if applicable
      if (showDiscountColumn && line.discount_percent) {
        lineTotal = lineTotal * (1 - line.discount_percent / 100);
      }
      
      onLineChange(lineId, 'line_total', lineTotal);
      
      // Calculate tax if applicable
      if (showTaxColumn && line.tax_rate) {
        const taxAmount = (lineTotal * line.tax_rate) / 100;
        onLineChange(lineId, 'tax_amount', taxAmount);
      }
    }
  };

  const handleUnitPriceChange = (lineId: string, unitPrice: number) => {
    onLineChange(lineId, 'unit_price', unitPrice);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      let lineTotal = line.quantity * unitPrice;
      
      // Apply discount if applicable
      if (showDiscountColumn && line.discount_percent) {
        lineTotal = lineTotal * (1 - line.discount_percent / 100);
      }
      
      onLineChange(lineId, 'line_total', lineTotal);
      
      // Calculate tax if applicable
      if (showTaxColumn && line.tax_rate) {
        const taxAmount = (lineTotal * line.tax_rate) / 100;
        onLineChange(lineId, 'tax_amount', taxAmount);
      }
    }
  };

  const handleTaxRateChange = (lineId: string, taxRate: number) => {
    onLineChange(lineId, 'tax_rate', taxRate);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const taxAmount = (line.line_total * taxRate) / 100;
      onLineChange(lineId, 'tax_amount', taxAmount);
    }
  };

  return (
    <Box>
      <TableContainer
        component={Paper}
        variant="outlined"
        sx={{
          width: '100%',
          overflowX: 'auto',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          borderRadius: '12px',
          border: '1px solid #e0e0e0',
          '& .MuiTable-root': {
            minWidth: 1000,
          },
          '& .MuiTableCell-root': {
            padding: '12px 16px',
            border: '1px solid #f0f0f0',
            fontSize: '0.9rem',
          },
          '& .MuiTableCell-head': {
            backgroundColor: '#f8f9fa',
            fontWeight: 600,
            color: '#2c3e50',
            borderBottom: '2px solid #dee2e6',
            fontSize: '0.95rem',
          },
          '& .MuiTableRow-root:hover': {
            backgroundColor: '#f8f9fa',
          },
          '& .MuiTableRow-root:nth-of-type(even)': {
            backgroundColor: '#fafafa',
          },
        }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell width={40}>#</TableCell>
              <TableCell width={200}>Product</TableCell>
              <TableCell width={250}>Description</TableCell>
              <TableCell width={100} align="center">Quantity</TableCell>
              <TableCell width={120} align="right">Unit Price</TableCell>
              {showDiscountColumn && (
                <TableCell width={100} align="right">Discount %</TableCell>
              )}
              {showTaxColumn && (
                <TableCell width={100} align="right">Output Tax %</TableCell>
              )}
              <TableCell width={120} align="right">Amount</TableCell>
              {!readOnly && (
                <TableCell width={60} align="center">Actions</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => (
              <TableRow key={line.id}>
                <TableCell align="center" sx={{ fontWeight: 500, color: '#666' }}>
                  {index + 1}
                </TableCell>

                {/* Product Column */}
                <TableCell>
                  <Autocomplete
                    options={products}
                    getOptionLabel={(option) => `${option.sku || ''} - ${option.name}`.trim()}
                    value={products.find(p => p.id === line.product) || null}
                    onChange={(_, value) => handleProductSelect(line.id, value)}
                    disabled={readOnly}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select product"
                        size="small"
                        fullWidth
                      />
                    )}
                    size="small"
                  />
                </TableCell>

                {/* Description Column */}
                <TableCell>
                  <TextField
                    value={line.description}
                    onChange={(e) => onLineChange(line.id, 'description', e.target.value)}
                    placeholder="Enter description"
                    size="small"
                    fullWidth
                    disabled={readOnly}
                  />
                </TableCell>
                
                <TableCell align="center">
                  <QuantityInput
                    value={line.quantity.toString()}
                    onChange={(e) => handleQuantityChange(line.id, parseFloat(e.target.value) || 0)}
                    disabled={readOnly}
                    size="small"
                    fullWidth
                    min={0}
                    precision={2}
                  />
                </TableCell>
                
                <TableCell align="right">
                  <FormattedCurrencyInput
                    name={`unit_price_${line.id}`}
                    value={line.unit_price}
                    onChange={(e) => handleUnitPriceChange(line.id, parseFloat(e.target.value) || 0)}
                    currencySymbol={currencySymbol}
                    disabled={readOnly}
                    size="small"
                    fullWidth
                  />
                </TableCell>
                
                {showDiscountColumn && (
                  <TableCell align="right">
                    <TextField
                      type="number"
                      value={line.discount_percent || 0}
                      onChange={(e) => onLineChange(line.id, 'discount_percent', parseFloat(e.target.value) || 0)}
                      disabled={readOnly}
                      size="small"
                      fullWidth
                      inputProps={{ min: 0, max: 100, step: 0.01 }}
                      InputProps={{
                        endAdornment: '%',
                      }}
                    />
                  </TableCell>
                )}
                
                {showTaxColumn && (
                  <TableCell align="right">
                    <TextField
                      type="number"
                      value={line.tax_rate || defaultTaxRate}
                      onChange={(e) => handleTaxRateChange(line.id, parseFloat(e.target.value) || 0)}
                      disabled={readOnly}
                      size="small"
                      fullWidth
                      inputProps={{ min: 0, max: 100, step: 0.01 }}
                      InputProps={{
                        endAdornment: '%',
                      }}
                    />
                  </TableCell>
                )}
                
                <TableCell align="right">
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {currencySymbol}{line.line_total.toFixed(2)}
                  </Typography>
                </TableCell>
                
                {!readOnly && (
                  <TableCell align="center">
                    <IconButton
                      onClick={() => onRemoveLine(line.id)}
                      size="small"
                      color="error"
                      disabled={lines.length <= 1}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {!readOnly && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-start' }}>
          <Button
            startIcon={<AddIcon />}
            onClick={onAddLine}
            variant="outlined"
            size="small"
          >
            Add Line Item
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default InvoiceLineTable;
