from django.core.management.base import BaseCommand
from django.db import connection
import sqlite3

class Command(BaseCommand):
    help = 'Fix foreign key constraints pointing to old sales_customers table'

    def handle(self, *args, **options):
        self.stdout.write('=== Fixing Customer Foreign Key Constraints ===')
        
        try:
            # Get database path
            from django.conf import settings
            db_path = settings.DATABASES['default']['NAME']
            
            # Connect directly to SQLite
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check current foreign key constraints
            cursor.execute("PRAGMA foreign_key_list(sales_invoices);")
            fks_before = cursor.fetchall()
            
            self.stdout.write("Current foreign key constraints:")
            for fk in fks_before:
                self.stdout.write(f"  - Column: {fk[3]} -> Table: {fk[2]}.{fk[4]}")
            
            # Disable foreign key checks temporarily
            cursor.execute("PRAGMA foreign_keys = OFF;")
            
            # Create new table with correct foreign key
            cursor.execute("""
                CREATE TABLE sales_invoices_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id CHAR(32) NOT NULL UNIQUE,
                    invoice_number VARCHAR(50) NOT NULL UNIQUE,
                    invoice_date DATE NOT NULL,
                    due_date DATE NOT NULL,
                    subtotal DECIMAL NOT NULL,
                    discount_percent DECIMAL NOT NULL,
                    discount_amount DECIMAL NOT NULL,
                    tax_amount DECIMAL NOT NULL,
                    total_amount DECIMAL NOT NULL,
                    amount_paid DECIMAL NOT NULL,
                    balance_due DECIMAL NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    payment_terms VARCHAR(20),
                    po_number VARCHAR(100),
                    memo TEXT,
                    message_to_customer TEXT,
                    email_sent BOOL NOT NULL,
                    email_sent_date DATETIME,
                    viewed_date DATETIME,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    created_by_id INTEGER,
                    customer_id BIGINT,
                    FOREIGN KEY (customer_id) REFERENCES contacts (id) ON DELETE SET NULL,
                    FOREIGN KEY (created_by_id) REFERENCES auth_user (id) ON DELETE SET NULL
                );
            """)
            
            # Copy data from old table to new table
            cursor.execute("""
                INSERT INTO sales_invoices_new 
                SELECT * FROM sales_invoices;
            """)
            
            # Drop old table
            cursor.execute("DROP TABLE sales_invoices;")
            
            # Rename new table
            cursor.execute("ALTER TABLE sales_invoices_new RENAME TO sales_invoices;")
            
            # Re-enable foreign key checks
            cursor.execute("PRAGMA foreign_keys = ON;")
            
            # Verify the fix
            cursor.execute("PRAGMA foreign_key_list(sales_invoices);")
            fks_after = cursor.fetchall()
            
            self.stdout.write("\nNew foreign key constraints:")
            for fk in fks_after:
                self.stdout.write(f"  - Column: {fk[3]} -> Table: {fk[2]}.{fk[4]}")
            
            # Commit changes
            conn.commit()
            conn.close()
            
            self.stdout.write(self.style.SUCCESS('✓ Successfully fixed customer foreign key constraints'))
            
            # Also fix other tables if needed
            self.fix_other_tables()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error: {str(e)}'))
            import traceback
            traceback.print_exc()
    
    def fix_other_tables(self):
        """Fix other tables that might have the same issue"""
        from django.conf import settings
        
        tables_to_fix = [
            'sales_estimates',
            'sales_payments', 
            'sales_orders',
            'sales_delivery_notes'
        ]
        
        conn = sqlite3.connect(settings.DATABASES['default']['NAME'])
        cursor = conn.cursor()
        
        for table_name in tables_to_fix:
            try:
                # Check if table exists
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
                if not cursor.fetchone():
                    continue
                
                # Check foreign key constraints
                cursor.execute(f"PRAGMA foreign_key_list({table_name});")
                fks = cursor.fetchall()
                
                needs_fix = any(fk[2] == 'sales_customers' for fk in fks)
                
                if needs_fix:
                    self.stdout.write(f"Fixing {table_name}...")
                    # Similar fix process for each table
                    # (Implementation would be similar but table-specific)
                    
            except Exception as e:
                self.stdout.write(f"Warning: Could not fix {table_name}: {str(e)}")
        
        conn.close() 