// src/components/pricing/PricingCalculator.tsx
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Grid,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { useSnackbar } from 'notistack';
import api from '../../../services/api';
import { pricingService, PriceCalculationResult } from '../../../services/pricingService';

interface Product {
  id: number;
  code: string;
  name: string;
}

interface Customer {
  id: number;
  contact: {
    name: string;
  };
}

const PricingCalculator: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [priceResult, setPriceResult] = useState<PriceCalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    product: '',
    customer: '',
    quantity: 1
  });

  // Fetch products and customers on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Ensure token is set
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
        }

        const [productsRes, customersRes] = await Promise.all([
          api.get('/pricing/products/'),
          api.get('/contacts/customers/'),
        ]);
        setProducts(productsRes.data?.results || productsRes.data || []);
        setCustomers(customersRes.data?.results || customersRes.data || []);
      } catch (error) {
        enqueueSnackbar('Failed to fetch data', { variant: 'error' });
      }
    };
    fetchData();
  }, [enqueueSnackbar]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculatePrice = async () => {
    if (!formData.product) {
      enqueueSnackbar('Please select a product', { variant: 'warning' });
      return;
    }

    try {
      setLoading(true);
      
      const params = {
        product: parseInt(formData.product),
        quantity: formData.quantity,
        customer: formData.customer ? parseInt(formData.customer) : undefined
      };
      
      const response = await pricingService.calculatePrice(params);
      setPriceResult(response.data);
      enqueueSnackbar('Price calculated successfully', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('Failed to calculate price', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const resultData = priceResult ? [
    { field: 'Product Code', value: priceResult.product_code },
    { field: 'Product Name', value: priceResult.product_name },
    { field: 'Customer', value: priceResult.customer_id ?
        customers.find(c => c.id === priceResult.customer_id)?.contact.name : 'None' },
    { field: 'Quantity', value: priceResult.quantity },
    { field: 'Sale Price', value: `$${priceResult.unit_price.toFixed(2)}` },
    { field: 'Cost Price', value: priceResult.cost_price ? `$${priceResult.cost_price.toFixed(2)}` : 'N/A' },
    { field: 'Margin ($)', value: priceResult.margin ? `$${priceResult.margin.toFixed(2)}` : 'N/A' },
    { field: 'Margin (%)', value: priceResult.margin_percent ? `${priceResult.margin_percent.toFixed(1)}%` : 'N/A' },
    { field: 'Currency', value: priceResult.currency },
  ] : [];

  return (
    <Card sx={{ margin: 2 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Pricing Calculator
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required>
              <InputLabel>Product</InputLabel>
              <Select
                value={formData.product}
                label="Product"
                onChange={(e) => handleInputChange('product', e.target.value)}
              >
                {(products || []).map(product => (
                  <MenuItem key={product.id || product.name} value={(product.id || '').toString()}>
                    {product.code || product.name} - {product.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Customer (optional)</InputLabel>
              <Select
                value={formData.customer}
                label="Customer (optional)"
                onChange={(e) => handleInputChange('customer', e.target.value)}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {(customers || []).map((customer: any) => (
                  <MenuItem key={customer.contact || customer.id} value={(customer.contact || customer.id || '').toString()}>
                    {customer.contact_name || customer.display_name || 'Unknown Customer'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Quantity"
              type="number"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 1)}
              inputProps={{ min: 0.01, step: 0.01 }}
              required
            />
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 3, mb: 2 }}>
          <Button 
            variant="contained" 
            onClick={calculatePrice}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Calculating...' : 'Calculate Price'}
          </Button>
        </Box>
        
        {priceResult && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Pricing Result
            </Typography>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell><strong>Field</strong></TableCell>
                    <TableCell><strong>Value</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {resultData.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.field}</TableCell>
                      <TableCell>{row.value}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default PricingCalculator;
