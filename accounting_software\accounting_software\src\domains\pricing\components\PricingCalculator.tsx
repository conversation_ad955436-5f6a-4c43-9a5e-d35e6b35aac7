// src/components/pricing/PricingCalculator.tsx
import React, { useState, useEffect } from 'react';
import { Card, Form, InputNumber, Select, Table, Typography, Button, message } from 'antd';
import axios from 'axios';
import { pricingService, PriceCalculationResult } from '../../../services/pricingService';

const { Title } = Typography;
const { Option } = Select;

interface Product {
  id: number;
  code: string;
  name: string;
}

interface Customer {
  id: number;
  contact: {
    name: string;
  };
}

const PricingCalculator: React.FC = () => {
  const [form] = Form.useForm();
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [priceResult, setPriceResult] = useState<PriceCalculationResult | null>(null);
  const [loading, setLoading] = useState(false);

  // Fetch products and customers on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsRes, customersRes] = await Promise.all([
          axios.get('/api/pricing/products/'),
          axios.get('/api/contacts/customers/'),
        ]);
        setProducts(productsRes.data);
        setCustomers(customersRes.data);
      } catch (error) {
        message.error('Failed to fetch data');
      }
    };
    fetchData();
  }, []);

  const calculatePrice = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const params = {
        product: values.product,
        quantity: values.quantity,
        customer: values.customer
      };
      
      const response = await pricingService.calculatePrice(params);
      setPriceResult(response.data);
    } catch (error) {
      message.error('Failed to calculate price');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Field',
      dataIndex: 'field',
      key: 'field',
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
    },
  ];

  const data = priceResult ? [
    { field: 'Product Code', value: priceResult.product_code },
    { field: 'Product Name', value: priceResult.product_name },
    { field: 'Customer', value: priceResult.customer_id ? 
        customers.find(c => c.id === priceResult.customer_id)?.contact.name : 'None' },
    { field: 'Quantity', value: priceResult.quantity },
    { field: 'Unit Price', value: `$${priceResult.unit_price.toFixed(2)}` },
    { field: 'Currency', value: priceResult.currency },
  ] : [];

  return (
    <Card title="Pricing Calculator" style={{ margin: '20px' }}>
      <Form form={form} layout="vertical">
        <Form.Item
          name="product"
          label="Product"
          rules={[{ required: true, message: 'Please select a product' }]}
        >
          <Select showSearch optionFilterProp="children" placeholder="Select product">
            {products.map(product => (
              <Option key={product.id} value={product.id}>
                {product.code} - {product.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="customer"
          label="Customer (optional)"
        >
          <Select showSearch optionFilterProp="children" placeholder="Select customer">
            {customers.map(customer => (
              <Option key={customer.id} value={customer.id}>
                {customer.contact.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="quantity"
          label="Quantity"
          initialValue={1}
          rules={[{ required: true, message: 'Please enter quantity' }]}
        >
          <InputNumber 
            style={{ width: '100%' }}
            min={0.01}
            step={0.01}
            precision={2}
          />
        </Form.Item>
        
        <Button 
          type="primary" 
          onClick={calculatePrice}
          loading={loading}
          style={{ marginBottom: 16 }}
        >
          Calculate Price
        </Button>
      </Form>
      
      {priceResult && (
        <div style={{ marginTop: '20px' }}>
          <Title level={4}>Pricing Result</Title>
          <Table 
            columns={columns}
            dataSource={data}
            rowKey="field"
            pagination={false}
            bordered
            size="small"
          />
        </div>
      )}
    </Card>
  );
};

export default PricingCalculator;
