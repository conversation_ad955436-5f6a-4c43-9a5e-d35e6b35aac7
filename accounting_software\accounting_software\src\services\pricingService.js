// src/services/pricingService.js
import axios from 'axios';

const API_BASE = '/api/pricing';

export const pricingService = {
  // Price Lists
  getPriceLists: () => axios.get('/api/price-lists/'),
  getPriceList: (id) => axios.get(`/api/price-lists/${id}/`),
  createPriceList: (data) => axios.post('/api/price-lists/', data),
  updatePriceList: (id, data) => axios.put(`/api/price-lists/${id}/`, data),
  deletePriceList: (id) => axios.delete(`/api/price-lists/${id}/`),
  
  // Price List Items
  getPriceListItems: (params) => axios.get('/api/price-list-items/', { params }),
  createPriceListItem: (data) => axios.post('/api/price-list-items/', data),
  updatePriceListItem: (id, data) => axios.put(`/api/price-list-items/${id}/`, data),
  deletePriceListItem: (id) => axios.delete(`/api/price-list-items/${id}/`),
  
  // Discount Rules
  getDiscountRules: (params) => axios.get('/api/discount-rules/', { params }),
  createDiscountRule: (data) => axios.post('/api/discount-rules/', data),
  updateDiscountRule: (id, data) => axios.put(`/api/discount-rules/${id}/`, data),
  deleteDiscountRule: (id) => axios.delete(`/api/discount-rules/${id}/`),
  
  // Pricing Calculations
  calculatePrice: (params) => axios.get(`${API_BASE}/get_price/`, { params }),
  
  // Default Price List
  getDefaultPriceList: () => axios.get('/api/price-lists/default/'),
};