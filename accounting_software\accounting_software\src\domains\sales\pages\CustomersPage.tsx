import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import { 
  Add as AddIcon, 
  TrendingUp, 
  Group, 
  AttachMoney, 
  Timeline,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Phone as PhoneIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import CustomerForm from '../components/CustomerForm';
import PaymentTermsModal from '../components/PaymentTermsModal';
import StatCard from '../../../shared/components/StatCard';
import DataTable, { commonRowActions, RowAction, Column } from '../../../shared/components/DataTable';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency } from '../../../shared/utils/formatters';

interface Customer {
  id: number;
  customer_id: string;
  display_name: string;
  customer_type: 'individual' | 'business' | 'vendor' | 'employee';
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
  current_balance: number;
  credit_limit?: number;
  payment_terms: string;
  currency: string;
  status: 'active' | 'inactive';
  taxable: boolean;
  gstin?: string;
  notes?: string;
  created_at: string;
}

interface CustomerStats {
  total_customers: number;
  active_customers: number;
  inactive_customers: number;
  total_receivables: number;
  new_customers_30_days: number;
}

const CustomersPage: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [stats, setStats] = useState<CustomerStats | null>(null);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuCustomer, setMenuCustomer] = useState<Customer | null>(null);
  const [paymentTermsModalOpen, setPaymentTermsModalOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewCustomer, setViewCustomer] = useState<Customer | null>(null);

  // Define columns for DataTable
  const columns: Column[] = [
    {
      field: 'display_name',
      headerName: 'Customer Name',
      width: 250,
      renderCell: ({ row }) => (
        <Box display="flex" alignItems="center">
          <Avatar sx={{ mr: 2, bgcolor: getCustomerTypeColor(row.customer_type) + '.light' }}>
            {getCustomerIcon(row)}
          </Avatar>
          <Box>
            <Typography variant="body1" fontWeight="medium">
              {row.display_name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {row.customer_id}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'customer_type',
      headerName: 'Type',
      width: 120,
      renderCell: ({ row }) => (
        <Chip
          size="small"
          label={row.customer_type}
          color={getCustomerTypeColor(row.customer_type)}
          variant="outlined"
        />
      ),
    },
    {
      field: 'email',
      headerName: 'Contact',
      width: 200,
      renderCell: ({ row }) => (
        <Box>
          <Typography variant="body2">{row.email}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.phone}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'billing_city',
      headerName: 'Location',
      width: 150,
      renderCell: ({ row }) => (
        <Box>
          <Typography variant="body2">{row.billing_city}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.billing_state}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'current_balance',
      headerName: 'Balance',
      width: 120,
      align: 'right',
      renderCell: ({ row }) => (
        <Typography
          variant="body2"
          color={row.current_balance < 0 ? 'error.main' : 'text.primary'}
          fontWeight="medium"
        >
          {formatCurrency(row.current_balance, row.currency)}
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 100,
      align: 'center',
      renderCell: ({ row }) => (
        <Chip
          size="small"
          label={row.status}
          color={row.status === 'active' ? 'success' : 'default'}
          icon={row.status === 'active' ? <CheckCircleIcon /> : <CancelIcon />}
        />
      ),
    },
  ];

  // Load customers from API
  const loadCustomers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: page.toString(),
        search: searchTerm,
        ordering: '-created_at',
      });

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      if (typeFilter !== 'all') {
        params.append('customer_type', typeFilter);
      }

      const response = await fetch(`http://localhost:8000/api/contacts/customers/?${params}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load customers');
      }

      const data = await response.json();
      setCustomers(data.results || []);
      setTotalCount(data.count || 0);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load customers');
      console.error('Load customers error:', err);
    } finally {
      setLoading(false);
    }
  }, [page, searchTerm, statusFilter, typeFilter]);

  // Load customer statistics
  const loadStats = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/contacts/customers/stats/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (err) {
      console.error('Load stats error:', err);
    }
  }, []);

  useEffect(() => {
    loadCustomers();
  }, [loadCustomers]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  const handleOpenDialog = useCallback(() => {
    setSelectedCustomer(null);
    setOpenDialog(true);
  }, []);
  
  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedCustomer(null);
  }, []);
  
  const handleEditCustomer = useCallback((customer: Customer) => {
    // Transform customer data to match form structure
    const customerForForm = {
      id: customer.id,
      firstName: customer.first_name || '',
      lastName: customer.last_name || '',
      displayName: customer.display_name,
      companyName: customer.company_name || '',
      email: customer.email || '',
      phone: customer.phone || '',
      mobile: customer.mobile || '',
      billingAddress: {
        street: customer.billing_street || '',
        city: customer.billing_city || '',
        state: customer.billing_state || '',
        postalCode: customer.billing_postal_code || '',
        country: customer.billing_country || 'US',
      },
      shippingAddress: {
        sameAsBilling: true,
        street: customer.shipping_street || '',
        city: customer.shipping_city || '',
        state: customer.shipping_state || '',
        postalCode: customer.shipping_postal_code || '',
        country: customer.shipping_country || 'US',
      },
      paymentTerms: customer.payment_terms || 'net_30',
      currency: customer.currency || 'USD',
      creditLimit: customer.credit_limit || 0,
      notes: customer.notes || '',
    };
    
    setSelectedCustomer(customerForForm);
    setOpenDialog(true);
  }, []);
  
  const handleDeleteCustomer = useCallback(async (customer: Customer) => {
    if (window.confirm(`Are you sure you want to delete ${customer.display_name}?\n\nThis action cannot be undone.`)) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:8000/api/sales/customers/${customer.id}/`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Token ${token}`,
          },
        });

        if (response.ok) {
          alert(`Customer "${customer.display_name}" has been deleted successfully.`);
          loadCustomers();
          loadStats();
        } else {
          const errorText = await response.text();
          throw new Error(`Failed to delete customer: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error deleting customer:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete customer';
        setError(errorMessage);
        alert(`Error: ${errorMessage}`);
      }
    }
  }, [loadCustomers, loadStats]);
  
  const handleViewCustomer = useCallback((customer: Customer) => {
    setViewCustomer(customer);
    setViewDialogOpen(true);
  }, []);
  
  const handleEmailCustomer = useCallback((customer: Customer) => {
    if (customer.email) {
      window.open(`mailto:${customer.email}?subject=Regarding your account&body=Dear ${customer.display_name},%0D%0A%0D%0A`);
    } else {
      alert('No email address available for this customer.');
    }
  }, []);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, customer: Customer) => {
    setAnchorEl(event.currentTarget);
    setMenuCustomer(customer);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuCustomer(null);
  };

  const handleSaveCustomer = useCallback(async () => {
    // This function is called after CustomerForm successfully saves
    // We just need to refresh the data
    loadCustomers();
    loadStats();
  }, [loadCustomers, loadStats]);

  const getCustomerIcon = (customer: Customer) => {
    switch (customer.customer_type) {
      case 'business':
        return <BusinessIcon />;
      case 'individual':
      default:
        return <PersonIcon />;
    }
  };

  const getCustomerTypeColor = (type: string) => {
    switch (type) {
      case 'business':
        return 'primary';
      case 'individual':
        return 'success';
      case 'vendor':
        return 'warning';
      case 'employee':
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading && customers.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Customers...</Typography>
      </Box>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4">
          👥 Customers
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<ReceiptIcon />}
            onClick={() => setPaymentTermsModalOpen(true)}
            size="small"
          >
            Payment Terms
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenDialog}
          >
            New Customer
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Customers"
              value={stats.total_customers}
              icon={<Group />}
              trend={stats.new_customers_30_days > 0 ? stats.new_customers_30_days : undefined}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Active Customers"
              value={stats.active_customers}
              icon={<TrendingUp />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Receivables"
              value={formatCurrency(stats.total_receivables, 'INR')}
              icon={<AttachMoney />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="New This Month"
              value={stats.new_customers_30_days}
              icon={<Timeline />}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={typeFilter}
                  label="Type"
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="individual">Individual</MenuItem>
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="vendor">Vendor</MenuItem>
                  <MenuItem value="employee">Employee</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadCustomers}
                size="small"
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardContent>
          <DataTable
            columns={columns}
            rows={customers || []}
            loading={loading}
            title="Customers"
            onRefresh={loadCustomers}
            checkboxSelection
            rowActions={[
              {
                icon: <EditIcon />,
                label: 'Edit',
                onClick: handleEditCustomer,
              },
              {
                icon: <VisibilityIcon />,
                label: 'View',
                onClick: handleViewCustomer,
              },
              {
                icon: <EmailIcon />,
                label: 'Email',
                onClick: handleEmailCustomer,
                hide: (row) => !row.email,
              },
              {
                icon: <DeleteIcon />,
                label: 'Delete',
                onClick: handleDeleteCustomer,
                color: 'error' as const,
              },
            ]}
          />
        </CardContent>
      </Card>

      {/* Customer Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <CustomerForm
          onClose={handleCloseDialog}
          onSave={handleSaveCustomer}
          initialValues={selectedCustomer || undefined}
        />
      </Dialog>

      {/* Payment Terms Modal */}
      <PaymentTermsModal
        open={paymentTermsModalOpen}
        onClose={() => setPaymentTermsModalOpen(false)}
      />

      {/* View Customer Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2 }}>
          <Typography variant="h5" fontWeight="bold">
            Customer Details
          </Typography>
        </DialogTitle>
        <DialogContent dividers sx={{ p: 3 }}>
          {viewCustomer && (
            <Grid container spacing={3}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom color="primary">
                  Basic Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Customer ID</Typography>
                <Typography variant="body1" fontWeight="medium">{viewCustomer.customer_id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Display Name</Typography>
                <Typography variant="body1" fontWeight="medium">{viewCustomer.display_name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">First Name</Typography>
                <Typography variant="body1">{viewCustomer.first_name || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Last Name</Typography>
                <Typography variant="body1">{viewCustomer.last_name || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Company</Typography>
                <Typography variant="body1">{viewCustomer.company_name || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Type</Typography>
                <Chip 
                  label={viewCustomer.customer_type} 
                  color={getCustomerTypeColor(viewCustomer.customer_type)}
                  size="small"
                />
              </Grid>

              {/* Contact Information */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  Contact Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Email</Typography>
                <Typography variant="body1">{viewCustomer.email || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Phone</Typography>
                <Typography variant="body1">{viewCustomer.phone || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Mobile</Typography>
                <Typography variant="body1">{viewCustomer.mobile || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Website</Typography>
                <Typography variant="body1">{viewCustomer.website || 'N/A'}</Typography>
              </Grid>

              {/* Billing Address */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  Billing Address
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body1">
                  {[
                    viewCustomer.billing_street,
                    viewCustomer.billing_city,
                    viewCustomer.billing_state,
                    viewCustomer.billing_postal_code,
                    viewCustomer.billing_country
                  ].filter(Boolean).join(', ') || 'No billing address provided'}
                </Typography>
              </Grid>

              {/* Financial Information */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  Financial Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Current Balance</Typography>
                <Typography 
                  variant="body1" 
                  fontWeight="medium"
                  color={viewCustomer.current_balance < 0 ? 'error.main' : 'text.primary'}
                >
                  {formatCurrency(viewCustomer.current_balance, viewCustomer.currency)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Credit Limit</Typography>
                <Typography variant="body1">
                  {viewCustomer.credit_limit ? formatCurrency(viewCustomer.credit_limit, viewCustomer.currency) : 'No limit'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Payment Terms</Typography>
                <Typography variant="body1">{viewCustomer.payment_terms}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Currency</Typography>
                <Typography variant="body1">{viewCustomer.currency}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Status</Typography>
                <Chip 
                  label={viewCustomer.status} 
                  color={viewCustomer.status === 'active' ? 'success' : 'default'}
                  size="small"
                  icon={viewCustomer.status === 'active' ? <CheckCircleIcon /> : <CancelIcon />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Taxable</Typography>
                <Typography variant="body1">{viewCustomer.taxable ? 'Yes' : 'No'}</Typography>
              </Grid>

              {/* Notes */}
              {viewCustomer.notes && (
                <>
                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Notes
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body1">{viewCustomer.notes}</Typography>
                  </Grid>
                </>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          {viewCustomer && (
            <>
              <Button 
                variant="outlined" 
                startIcon={<EditIcon />}
                onClick={() => {
                  setViewDialogOpen(false);
                  handleEditCustomer(viewCustomer);
                }}
              >
                Edit
              </Button>
              {viewCustomer.email && (
                <Button 
                  variant="contained" 
                  startIcon={<EmailIcon />}
                  onClick={() => handleEmailCustomer(viewCustomer)}
                >
                  Send Email
                </Button>
              )}
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (menuCustomer) handleEditCustomer(menuCustomer);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Customer</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuCustomer) handleViewCustomer(menuCustomer);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuCustomer && menuCustomer.email) handleEmailCustomer(menuCustomer);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EmailIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Send Email</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          if (menuCustomer) handleDeleteCustomer(menuCustomer);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Customer</ListItemText>
        </MenuItem>
      </Menu>
    </PageContainer>
  );
};

export default CustomersPage;