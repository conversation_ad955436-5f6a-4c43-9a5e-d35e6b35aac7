import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';

export interface ProductCategory {
  id: string;
  name: string;
  code: string;
  description: string;
  divisionType: 'perishable' | 'non-perishable' | 'refrigerated' | 'frozen' | 'controlled-substance';
  parentCategoryId?: string;
  level: number;
  imageUrl?: string;
  taxCategory?: string;
  marginPercentage?: number;
  isActive: boolean;
  sortOrder: number;
  allowSubcategories: boolean;
  requiresExpiryTracking?: boolean;
  requiresBatchTracking?: boolean;
  defaultUnitOfMeasure?: string;
  createdAt: string;
  updatedAt: string;
  // Related data
  division?: {
    id: string;
    name: string;
    type: string;
  };
  parentCategory?: {
    id: string;
    name: string;
  };
  subcategoriesCount?: number;
  productsCount?: number;
}

export interface ProductCategoryFormData {
  name: string;
  code: string;
  description: string;
  divisionType: 'perishable' | 'non-perishable' | 'refrigerated' | 'frozen' | 'controlled-substance';
  parentCategoryId: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  marginPercentage: number;
  taxCategory: string;
  allowSubcategories: boolean;
  requiresExpiryTracking: boolean;
  requiresBatchTracking: boolean;
  defaultUnitOfMeasure: string;
}

interface ProductCategoriesContextType {
  categories: ProductCategory[];
  loading: boolean;
  error: string | null;
  loadCategories: () => Promise<void>;
  createCategory: (categoryData: ProductCategoryFormData) => Promise<ProductCategory>;
  updateCategory: (id: string, categoryData: ProductCategoryFormData) => Promise<ProductCategory>;
  deleteCategory: (id: string) => Promise<void>;
  getCategoryById: (id: string) => ProductCategory | undefined;
  getCategoriesByDivision: (divisionId: string) => ProductCategory[];
  getSubcategories: (parentId: string) => ProductCategory[];
}

const ProductCategoriesContext = createContext<ProductCategoriesContextType | undefined>(undefined);

interface ProductCategoriesProviderProps {
  children: ReactNode;
}

export const ProductCategoriesProvider: React.FC<ProductCategoriesProviderProps> = ({ children }) => {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategories = useCallback(async () => {
    console.log('🔄 Loading categories from API');
    try {
      setLoading(true);
      setError(null);

      // Ensure token is set
      if (!localStorage.getItem('token')) {
        localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
      }
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/sales/categories/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load categories: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Categories loaded from database:', data);
      
      // Transform backend data to frontend format
      const transformedCategories: ProductCategory[] = data.results?.map((cat: any) => ({
        id: cat.id.toString(),
        name: cat.name,
        code: cat.code,
        description: cat.description || '',
        divisionType: cat.division_type,
        parentCategoryId: cat.parent_category?.toString(),
        level: cat.level,
        imageUrl: cat.image_url,
        taxCategory: cat.tax_category,
        marginPercentage: cat.margin_percentage || 0,
        isActive: cat.is_active,
        sortOrder: cat.sort_order,
        allowSubcategories: cat.allow_subcategories,
        requiresExpiryTracking: cat.requires_expiry_tracking || false,
        requiresBatchTracking: cat.requires_batch_tracking || false,
        defaultUnitOfMeasure: cat.default_unit_of_measure || 'piece',
        createdAt: cat.created_at,
        updatedAt: cat.updated_at,
        subcategoriesCount: cat.subcategories_count || 0,
        productsCount: cat.products_count || 0,
        parentCategory: cat.parent_category_name ? {
          id: cat.parent_category.toString(),
          name: cat.parent_category_name
        } : undefined,
      })) || [];

      setCategories(transformedCategories);
      console.log(`✅ ${transformedCategories.length} categories loaded successfully`);
      
    } catch (err) {
      console.error('❌ Error loading categories:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load categories';
      setError(errorMessage);
      
      // Fallback to empty array on error
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const createCategory = async (categoryData: ProductCategoryFormData): Promise<ProductCategory> => {
    try {
      setLoading(true);
      setError(null);

      // Ensure token is set
      if (!localStorage.getItem('token')) {
        localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
      }
      const token = localStorage.getItem('token');
      
      // Transform frontend data to backend format
      const backendData = {
        name: categoryData.name,
        code: categoryData.code,
        description: categoryData.description,
        division_type: categoryData.divisionType,
        parent_category: categoryData.parentCategoryId ? parseInt(categoryData.parentCategoryId) : null,
        image_url: categoryData.imageUrl || null,
        tax_category: categoryData.taxCategory || null,
        margin_percentage: categoryData.marginPercentage,
        is_active: categoryData.isActive,
        sort_order: categoryData.sortOrder,
        allow_subcategories: categoryData.allowSubcategories,
        requires_expiry_tracking: categoryData.requiresExpiryTracking,
        requires_batch_tracking: categoryData.requiresBatchTracking,
        default_unit_of_measure: categoryData.defaultUnitOfMeasure,
      };

      const response = await fetch('http://localhost:8000/api/sales/categories/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(backendData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to create category: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const createdCategory = await response.json();
      console.log('✅ Category created:', createdCategory);

      // Transform response back to frontend format
      const newCategory: ProductCategory = {
        id: createdCategory.id.toString(),
        name: createdCategory.name,
        code: createdCategory.code,
        description: createdCategory.description || '',
        divisionType: createdCategory.division_type,
        parentCategoryId: createdCategory.parent_category?.toString(),
        level: createdCategory.level,
        imageUrl: createdCategory.image_url,
        taxCategory: createdCategory.tax_category,
        marginPercentage: createdCategory.margin_percentage || 0,
        isActive: createdCategory.is_active,
        sortOrder: createdCategory.sort_order,
        allowSubcategories: createdCategory.allow_subcategories,
        requiresExpiryTracking: createdCategory.requires_expiry_tracking || false,
        requiresBatchTracking: createdCategory.requires_batch_tracking || false,
        defaultUnitOfMeasure: createdCategory.default_unit_of_measure || 'piece',
        createdAt: createdCategory.created_at,
        updatedAt: createdCategory.updated_at,
        subcategoriesCount: 0,
        productsCount: 0,
        parentCategory: createdCategory.parent_category_name ? {
          id: createdCategory.parent_category.toString(),
          name: createdCategory.parent_category_name
        } : undefined,
      };
      
      // Add to local state
      setCategories(prev => [...prev, newCategory]);
      
      return newCategory;
    } catch (err) {
      console.error('❌ Error creating category:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create category';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateCategory = async (id: string, categoryData: ProductCategoryFormData): Promise<ProductCategory> => {
    try {
      setLoading(true);
      setError(null);

      // Ensure token is set
      if (!localStorage.getItem('token')) {
        localStorage.setItem('token', 'e15bc01f831c5111f413f534ef82288744cb7d41');
      }
      const token = localStorage.getItem('token');
      
      // Transform frontend data to backend format
      const backendData = {
        name: categoryData.name,
        code: categoryData.code,
        description: categoryData.description,
        division_type: categoryData.divisionType,
        parent_category: categoryData.parentCategoryId ? parseInt(categoryData.parentCategoryId) : null,
        image_url: categoryData.imageUrl || null,
        tax_category: categoryData.taxCategory || null,
        margin_percentage: categoryData.marginPercentage,
        is_active: categoryData.isActive,
        sort_order: categoryData.sortOrder,
        allow_subcategories: categoryData.allowSubcategories,
        requires_expiry_tracking: categoryData.requiresExpiryTracking,
        requires_batch_tracking: categoryData.requiresBatchTracking,
        default_unit_of_measure: categoryData.defaultUnitOfMeasure,
      };

      const response = await fetch(`http://localhost:8000/api/sales/categories/${id}/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(backendData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to update category: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const updatedCategory = await response.json();
      console.log('✅ Category updated:', updatedCategory);

      // Transform response back to frontend format
      const transformedCategory: ProductCategory = {
        id: updatedCategory.id.toString(),
        name: updatedCategory.name,
        code: updatedCategory.code,
        description: updatedCategory.description || '',
        divisionType: updatedCategory.division_type,
        parentCategoryId: updatedCategory.parent_category?.toString(),
        level: updatedCategory.level,
        imageUrl: updatedCategory.image_url,
        taxCategory: updatedCategory.tax_category,
        marginPercentage: updatedCategory.margin_percentage || 0,
        isActive: updatedCategory.is_active,
        sortOrder: updatedCategory.sort_order,
        allowSubcategories: updatedCategory.allow_subcategories,
        requiresExpiryTracking: updatedCategory.requires_expiry_tracking || false,
        requiresBatchTracking: updatedCategory.requires_batch_tracking || false,
        defaultUnitOfMeasure: updatedCategory.default_unit_of_measure || 'piece',
        createdAt: updatedCategory.created_at,
        updatedAt: updatedCategory.updated_at,
        subcategoriesCount: updatedCategory.subcategories_count || 0,
        productsCount: updatedCategory.products_count || 0,
        parentCategory: updatedCategory.parent_category_name ? {
          id: updatedCategory.parent_category.toString(),
          name: updatedCategory.parent_category_name
        } : undefined,
      };
      
      // Update local state
      setCategories(prev => prev.map(cat => cat.id === id ? transformedCategory : cat));
      
      return transformedCategory;
    } catch (err) {
      console.error('❌ Error updating category:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update category';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteCategory = async (id: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // For development, just remove from local state
      const categoryToDelete = categories.find(cat => cat.id === id);
      if (!categoryToDelete) {
        throw new Error('Category not found');
      }

      // Remove from local state
      setCategories(prev => prev.filter(cat => cat.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete category';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getCategoryById = (id: string): ProductCategory | undefined => {
    return categories.find(category => category.id === id);
  };

  const getCategoriesByDivision = (divisionType: string): ProductCategory[] => {
    return categories.filter(category => category.divisionType === divisionType);
  };

  const getSubcategories = (parentId: string): ProductCategory[] => {
    return categories.filter(category => category.parentCategoryId === parentId);
  };

  useEffect(() => {
    if (categories.length === 0) {
    loadCategories();
    }
  }, []);

  const value: ProductCategoriesContextType = {
    categories,
    loading,
    error,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryById,
    getCategoriesByDivision,
    getSubcategories,
  };

  return (
    <ProductCategoriesContext.Provider value={value}>
      {children}
    </ProductCategoriesContext.Provider>
  );
};

export const useProductCategories = (): ProductCategoriesContextType => {
  const context = useContext(ProductCategoriesContext);
  if (context === undefined) {
    throw new Error('useProductCategories must be used within a ProductCategoriesProvider');
  }
  return context;
}; 