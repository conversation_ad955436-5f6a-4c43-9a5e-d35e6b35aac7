from django.core.management.base import BaseCommand
from django.db import transaction
from sales.models import Product as SalesProduct
from Pricing.models import Product as PricingProduct


class Command(BaseCommand):
    help = 'Sync products from Sales app to Pricing app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync even if pricing products exist',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        # Check if pricing products already exist
        pricing_count = PricingProduct.objects.count()
        if pricing_count > 0 and not force:
            self.stdout.write(
                self.style.WARNING(
                    f'Pricing app already has {pricing_count} products. '
                    'Use --force to sync anyway.'
                )
            )
            return

        # Get all sales products
        sales_products = SalesProduct.objects.all()
        self.stdout.write(f'Found {sales_products.count()} products in Sales app')

        synced_count = 0
        updated_count = 0

        with transaction.atomic():
            for sales_product in sales_products:
                # Check if pricing product already exists
                pricing_product, created = PricingProduct.objects.get_or_create(
                    code=sales_product.sku or f'PROD-{sales_product.id}',
                    defaults={
                        'name': sales_product.name,
                        'description': sales_product.description or '',
                        'standard_cost': sales_product.cost_price or 0,
                        'is_active': sales_product.status == 'active',
                    }
                )

                if created:
                    synced_count += 1
                    self.stdout.write(f'✅ Created: {pricing_product.name}')
                else:
                    # Update existing product
                    pricing_product.name = sales_product.name
                    pricing_product.description = sales_product.description or ''
                    pricing_product.standard_cost = sales_product.cost_price or 0
                    pricing_product.is_active = sales_product.status == 'active'
                    pricing_product.save()
                    updated_count += 1
                    self.stdout.write(f'🔄 Updated: {pricing_product.name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Sync completed!\n'
                f'Created: {synced_count} products\n'
                f'Updated: {updated_count} products\n'
                f'Total pricing products: {PricingProduct.objects.count()}'
            )
        )
